package br.gov.ac.tce.sicapanalise.util;

import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JRException;
import java.io.File;

/**
 * Utilitário para compilar relatórios JasperReports
 */
public class CompiladorRelatorio {
    
    public static void main(String[] args) {
        try {
            // Caminho base dos relatórios
            String basePath = "src/main/webapp/relatorios/acumulacao/";
            
            // Compila o novo subrelatório
            compilarRelatorio(basePath + "subRelatorioAcumulacaoVinculosMultiplas2.jrxml");

            // Compila o relatório principal
            compilarRelatorio(basePath + "relatorioAcumulacao.jrxml");
            
            System.out.println("Relatório compilado com sucesso!");
            
        } catch (Exception e) {
            System.err.println("Erro ao compilar relatório: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static void compilarRelatorio(String caminhoJrxml) throws JRException {
        File arquivoJrxml = new File(caminhoJrxml);
        if (!arquivoJrxml.exists()) {
            throw new RuntimeException("Arquivo não encontrado: " + caminhoJrxml);
        }
        
        String caminhoJasper = caminhoJrxml.replace(".jrxml", ".jasper");
        
        System.out.println("Compilando: " + caminhoJrxml);
        System.out.println("Destino: " + caminhoJasper);
        
        JasperCompileManager.compileReportToFile(caminhoJrxml, caminhoJasper);
        
        System.out.println("Compilação concluída: " + caminhoJasper);
    }
}
