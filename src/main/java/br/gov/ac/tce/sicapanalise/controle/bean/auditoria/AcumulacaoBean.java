package br.gov.ac.tce.sicapanalise.controle.bean.auditoria;

import java.io.Serializable;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import br.gov.ac.tce.message.MessageType;
import br.gov.ac.tce.message.Messenger;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.Acumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.PerfilGrupo;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.UsuarioGrupo;
import br.gov.ac.tce.sicapanalise.auditoria.business.AcumulacaoBusiness;
import br.gov.ac.tce.sicapanalise.auditoria.repositorio.UsuarioAuditoriaRepositorio;
import br.gov.ac.tce.sicapanalise.controle.bean.LoginBean;
import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapanalise.util.Mensagem;
import br.gov.ac.tce.sicapanalise.util.MensagemType;
import br.gov.ac.tce.sicapweb.modelo.SituacaoAcumulacao;
import br.gov.ac.tce.sicapweb.modelo.CargaHorariaSemanalFiltro;
import br.gov.ac.tce.sicapweb.modelo.NumeroVinculosFiltro;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.repositorio.EntidadeRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.RelatorioConexao;
import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;

@Named
@ViewScoped
public class AcumulacaoBean implements Serializable {

	private static final long serialVersionUID = 1L;

	@Inject
	private LoginBean loginBean;
	@Inject
	private UsuarioAuditoriaRepositorio servidorTceRepositorio;

	@Inject
	private EntidadeRepositorio entidadeRepositorio;
	@Inject
	private RelatorioConexao relatorioConexao;
	
//	@Inject
//	private AnaliseBusiness analiseBusiness;
	
	@Inject
	private AcumulacaoBusiness acumulacaoBusiness;
	

	private Collection<Object[]> listaCompetencias;
	private Collection<Object[]> listaCargosAcumulacao;
	private Collection<Entidade> listaEntidades;

	private Collection<Acumulacao> listaAcumulacoes;

	private List<Acumulacao> acumulacoesSelecionadas;

	private List<String> competenciasSelecionadas;
	private String cpf;
	private String nome;
	private Integer entidade;
	private String situacao;
	private Integer cargo;
	private String cargaHorariaSemanalFiltro;
	private String numeroVinculosFiltro;

	private Acumulacao acumulacao;

	private int currentLevel;
	
	
	private String iniciarAnaliseDespacho;
	

	public int getCurrentLevel() {
		return currentLevel;
	}

	public void setCurrentLevel(int currentLevel) {
		this.currentLevel = currentLevel;
	}

	public Collection<Object[]> getListaCompetencias() {
		return listaCompetencias;
	}

	public void setListaCompetencias(Collection<Object[]> listaCompetencias) {
		this.listaCompetencias = listaCompetencias;
	}

	public List<String> getCompetenciasSelecionadas() {
		return competenciasSelecionadas;
	}

	public void setCompetenciasSelecionadas(List<String> competenciasSelecionadas) {
		this.competenciasSelecionadas = competenciasSelecionadas;
	}

	private String getCompetenciaParaConsulta() {
		if (competenciasSelecionadas == null || competenciasSelecionadas.isEmpty()) {
			return "0"; 
		}

		if (competenciasSelecionadas.size() == 1) {
			return competenciasSelecionadas.get(0);
		}
		return "MULTIPLAS";
	}

	public String getCpf() {
		return cpf;
	}

	public String getSituacao() {
		return situacao;
	}

	public void setSituacao(String situacao) {
		this.situacao = situacao;
	}

	public void setCpf(String cpf) {
		this.cpf = cpf;
	}
	
	public void pesquisarPorCpfENavegar(String cpf) {
		try {
			this.cpf = cpf;

			this.nome = "";
			this.entidade = 0;
			this.cargo = 0;
			this.situacao = null;
			this.competenciasSelecionadas = new ArrayList<>();
			this.cargaHorariaSemanalFiltro = "0";
			this.numeroVinculosFiltro = "0";

			this.listaAcumulacoes = this.acumulacaoBusiness.listaAcumulacoes(this.getCompetenciaParaConsulta(), this.situacao,
					this.cpf, this.nome, this.entidade, this.cargo, this.cargaHorariaSemanalFiltro, this.numeroVinculosFiltro);

			this.currentLevel = 1;
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível listar as acumulações para o CPF informado.");
		}
	}

	public void pesquisarPorCpfECompetencia(String cpf, String competencia) {
		try {
			this.cpf = cpf;
			this.competenciasSelecionadas = new ArrayList<>();
			this.competenciasSelecionadas.add(competencia);
			this.nome = "";
			this.entidade = 0;
			this.cargo = 0;
			this.situacao = null;
			this.cargaHorariaSemanalFiltro = "0";
			this.numeroVinculosFiltro = "0";

			this.listaAcumulacoes = this.acumulacaoBusiness.listaAcumulacoes(competencia, this.situacao,
					this.cpf, this.nome, this.entidade, this.cargo, this.cargaHorariaSemanalFiltro, this.numeroVinculosFiltro);

			this.currentLevel = 1;
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível listar as acumulações para a competência informada.");
		}
	}

	public List<String> dividirPeriodoConcomitancia(String periodoConcomitancia) {
		List<String> competencias = new ArrayList<>();
		if (periodoConcomitancia != null && !periodoConcomitancia.trim().isEmpty()) {
			String[] partes = periodoConcomitancia.split(",");
			for (String parte : partes) {
				String competencia = parte.trim();
				if (!competencia.isEmpty()) {
					competencias.add(competencia);
				}
			}
		}
		return competencias;
	}

	public List<SituacaoAcumulacao> getListaSituacaoAcumulacao() {
		List<SituacaoAcumulacao> situacaoAcumulacoes = Arrays.asList(SituacaoAcumulacao.values());
		Collections.sort(situacaoAcumulacoes, Comparator.comparing(SituacaoAcumulacao::getDescricao));
		return situacaoAcumulacoes;
	}

	public List<CargaHorariaSemanalFiltro> getListaCargaHorariaSemanalFiltro() {
		return Arrays.asList(CargaHorariaSemanalFiltro.values());
	}

	public List<NumeroVinculosFiltro> getListaNumeroVinculosFiltro() {
		return Arrays.asList(NumeroVinculosFiltro.values());
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}

	public Integer getEntidade() {
		return entidade;
	}

	public void setEntidade(Integer situacao) {
		this.entidade = situacao;
	}

	public Collection<Entidade> getListaEntidades() {
		return listaEntidades;
	}

	public Collection<Object[]> getListaCargosAcumulacao() {
		return listaCargosAcumulacao;
	}

	public void setListaCargosAcumulacao(Collection<Object[]> listaCargosAcumulacao) {
		this.listaCargosAcumulacao = listaCargosAcumulacao;
	}

	public Integer getCargo() {
		return cargo;
	}

	public void setCargo(Integer idCargo) {
		this.cargo = idCargo;
	}

	public Collection<Acumulacao> getListaAcumulacoes() {
		return listaAcumulacoes;
	}

	public void setListaAcumulacoes(Collection<Acumulacao> listaAcumulacoes) {
		this.listaAcumulacoes = listaAcumulacoes;
	}

	public List<Acumulacao> getAcumulacoesSelecionadas() {
		return acumulacoesSelecionadas;
	}

	public void setAcumulacoesSelecionadas(List<Acumulacao> acumulacoesSelecionadas) {
		this.acumulacoesSelecionadas = acumulacoesSelecionadas;
	}

	public Acumulacao getAcumulacao() {
		return acumulacao;
	}

	public void setAcumulacao(Acumulacao acumulacao) {
		this.acumulacao = acumulacao;
	}

	public String getIniciarAnaliseDespacho() {
		return iniciarAnaliseDespacho;
	}

	public void setIniciarAnaliseDespacho(String iniciarAnaliseDespacho) {
		this.iniciarAnaliseDespacho = iniciarAnaliseDespacho;
	}

	public String getCargaHorariaSemanalFiltro() {
		return cargaHorariaSemanalFiltro;
	}

	public void setCargaHorariaSemanalFiltro(String cargaHorariaSemanalFiltro) {
		this.cargaHorariaSemanalFiltro = cargaHorariaSemanalFiltro;
	}

	public String getNumeroVinculosFiltro() {
		return numeroVinculosFiltro;
	}

	public void setNumeroVinculosFiltro(String numeroVinculosFiltro) {
		this.numeroVinculosFiltro = numeroVinculosFiltro;
	}

	@PostConstruct
	public void init() {
		try {
			System.out.println("=== INICIANDO ACUMULACAO BEAN ===");
			this.currentLevel = 1;
			this.entidade = 0;
			this.cpf = "";
			this.nome = "";
			this.competenciasSelecionadas = new ArrayList<>();
			this.cargaHorariaSemanalFiltro = "0";
			this.numeroVinculosFiltro = "0";

			System.out.println("Carregando competências...");
			this.listaCompetencias = this.acumulacaoBusiness.listaCompetencias();
			System.out.println("Competências carregadas: " + (this.listaCompetencias != null ? this.listaCompetencias.size() : "null"));

			if (this.listaCompetencias != null) {
				System.out.println("=== DETALHES DAS COMPETÊNCIAS ===");
				for (Object[] competencia : this.listaCompetencias) {
					System.out.println("Competência: ano=" + competencia[0] + ", mes=" + competencia[1] + ", nomeMes=" + competencia[2]);
					System.out.println("  - itemValue seria: " + competencia[1] + "/" + competencia[0]);
					System.out.println("  - itemLabel seria: " + competencia[2] + "/" + competencia[0]);
				}
			}

			System.out.println("Carregando entidades...");
			this.listaEntidades = this.entidadeRepositorio.lista();
			System.out.println("Entidades carregadas: " + (this.listaEntidades != null ? this.listaEntidades.size() : "null"));

			this.iniciarAnaliseDespacho = "";
			
			this.listaAcumulacoes = null;
			System.out.println("Lista de acumulações inicializada como vazia.");

			if (this.loginBean != null && this.loginBean.getLogin() != null) {
				System.out.println("Atualizando usuário: " + this.loginBean.getLogin());
				this.loginBean.setUsuario(this.servidorTceRepositorio.retornaServidorPorLogin(this.loginBean.getLogin()));
			} else {
				System.out.println("LoginBean ou login é null!");
			}

			System.out.println("=== ACUMULACAO BEAN INICIALIZADO COM SUCESSO ===");
		} catch (RepositorioException e) {
			System.out.println("ERRO RepositorioException: " + e.getMessage());
			e.printStackTrace();
			Mensagem.setMensagem(MensagemType.ERRO, "Ocorreu um erro ao selecionar o filtro para a consulta.", "");
		} catch (Exception e) {
			System.out.println("ERRO Exception: " + e.getMessage());
			e.printStackTrace();
		}
	}

	public void pesquisar() {
		try {
			System.out.println("=== INICIANDO PESQUISA NO BEAN ===");
			System.out.println("Competências selecionadas: " + this.competenciasSelecionadas);
			System.out.println("Situação: " + this.situacao);
			System.out.println("CPF: " + this.cpf);
			System.out.println("Nome: " + this.nome);
			System.out.println("Entidade: " + this.entidade);
			System.out.println("Cargo: " + this.cargo);

			boolean consultaAmpla = isConsultaMuitoAmpla();
			if (consultaAmpla) {
				Messenger.mostrarMensagem(MessageType.AVISO,
					"Para evitar problemas de performance, refine sua pesquisa informando pelo menos um dos seguintes filtros: CPF, Nome, Entidade ou Cargo. " +
					"A consulta foi limitada aos primeiros 100000 resultados.");
			}

			Collection<Acumulacao> acumulacoes;

			if (this.competenciasSelecionadas != null && !this.competenciasSelecionadas.isEmpty()) {
				System.out.println("Usando método com múltiplas competências...");
				acumulacoes = this.acumulacaoBusiness.listaAcumulacoes(
					this.competenciasSelecionadas,
					this.situacao,
					this.cpf,
					this.nome,
					this.entidade,
					this.cargo,
					"0",
					this.numeroVinculosFiltro
				);
			} else {
				System.out.println("Usando método com competência única (padrão)...");
				acumulacoes = this.acumulacaoBusiness.listaAcumulacoes(
					"0",
					this.situacao,
					this.cpf,
					this.nome,
					this.entidade,
					this.cargo,
					"0",
					this.numeroVinculosFiltro
				);
			}

			this.listaAcumulacoes = filtrarPorCargaHoraria(acumulacoes, this.cargaHorariaSemanalFiltro);

			System.out.println("=== RESULTADO FINAL NO BEAN ===");
			System.out.println("Total de acumulações após filtros: " + (this.listaAcumulacoes != null ? this.listaAcumulacoes.size() : "null"));
			if (!consultaAmpla && this.listaAcumulacoes != null && this.listaAcumulacoes.size() >= 100000) {
				Messenger.mostrarMensagem(MessageType.AVISO,
					"A consulta retornou o limite máximo de 100000 registros. Pode haver mais resultados. " +
					"Refine os filtros para uma busca mais específica.");
			}

			this.currentLevel = 1;
		} catch (RepositorioException e) {
			System.out.println("ERRO no bean: " + e.getMessage());
			e.printStackTrace();
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível listar as acumulações.");
		}
	}

	private boolean isConsultaMuitoAmpla() {
		boolean temCpf = this.cpf != null && !this.cpf.trim().isEmpty();
		boolean temNome = this.nome != null && !this.nome.trim().isEmpty();
		boolean temEntidade = this.entidade != null && this.entidade > 0;
		boolean temCargo = this.cargo != null && this.cargo > 0;
		boolean temSituacao = this.situacao != null;

		return !temCpf && !temNome && !temEntidade && !temCargo && !temSituacao;
	}
	public void perquisarCargos() {
		try {
			System.out.println("=== PESQUISANDO CARGOS ===");
			System.out.println("Competências selecionadas: " + this.competenciasSelecionadas);
			System.out.println("Entidade: " + this.entidade);

			if (this.competenciasSelecionadas != null && !this.competenciasSelecionadas.isEmpty()) {
				System.out.println("Usando método com múltiplas competências para cargos...");
				this.listaCargosAcumulacao = this.acumulacaoBusiness.listaCargosAcumulacao(this.competenciasSelecionadas,
						this.entidade);
			} else {
				System.out.println("Usando método padrão para cargos...");
				this.listaCargosAcumulacao = this.acumulacaoBusiness.listaCargosAcumulacao("0",
						this.entidade);
			}

			System.out.println("Cargos encontrados: " + (this.listaCargosAcumulacao != null ? this.listaCargosAcumulacao.size() : "null"));

		} catch (RepositorioException e) {
			System.out.println("ERRO ao pesquisar cargos: " + e.getMessage());
			e.printStackTrace();
			Mensagem.setMensagem(MensagemType.ERRO, "Ocorreu um erro ao pesquisar os cargos.", "");
		}
	}
	

	public void gerarRelatorioAcumulacao() {
		String pathRelatorioDir = FacesContext.getCurrentInstance().getExternalContext().getRealPath("relatorios/")
				+ "/";
		String pathRelatorio = pathRelatorioDir + "acumulacao/" + "relatorioAcumulacao.jasper";

		HashMap<String, Object> parameters = new LinkedHashMap<String, Object>();
		parameters.put("cpf", this.acumulacao.getCpf()/* this.servidor[1] */);
		parameters.put("usuario", this.acumulacao.getNome()/* this.loginBean.getNome() */);
		parameters.put("exercicio", this.acumulacao.getAno()/* this.servidor[3] */);
		parameters.put("REPORT_IMAGE_DIR", pathRelatorioDir);
		parameters.put("SUBREPORT_DIR", pathRelatorioDir + "acumulacao/");
		parameters.put("mes", this.acumulacao.getMes()/* this.servidor[4] */);

		
		if (this.competenciasSelecionadas != null && this.competenciasSelecionadas.size() > 1) {
			// Para múltiplas competências
			String competenciasStr = String.join(", ", this.competenciasSelecionadas);
			parameters.put("competencias", competenciasStr);
			parameters.put("competencia", ""); 
		} else {
			String competencia = this.acumulacao.getMes() + "-" + this.acumulacao.getAno();
			parameters.put("competencia", competencia);
			parameters.put("competencias", ""); 
		}

		JasperPrint jasperPrint;
		try (Connection conexaoJasper = this.relatorioConexao.getConnection()) {
			HttpServletResponse httpServletResponse = (HttpServletResponse) FacesContext.getCurrentInstance()
					.getExternalContext().getResponse();

			// Define o nome do arquivo baseado no tipo de relatório
			String nomeArquivo;
			if (this.competenciasSelecionadas != null && this.competenciasSelecionadas.size() > 1) {
				nomeArquivo = "RelatorioAcumulacao-" + this.acumulacao.getCpf() + "-Multiplas-" + System.currentTimeMillis() + ".pdf";
			} else {
				String competencia = this.acumulacao.getMes() + "-" + this.acumulacao.getAno();
				nomeArquivo = "RelatorioAcumulacao-" + this.acumulacao.getCpf() + "-" + competencia + ".pdf";
			}

			httpServletResponse.addHeader("Content-disposition", "attachment; filename=" + nomeArquivo);
			ServletOutputStream arquivo = httpServletResponse.getOutputStream();

			
			jasperPrint = JasperFillManager.fillReport(pathRelatorio, parameters,
					conexaoJasper);
			JasperExportManager.exportReportToPdfStream(jasperPrint, arquivo);
			arquivo.flush();
			arquivo.close();

			FacesContext.getCurrentInstance().responseComplete();

			// JRExporter exporter = new JRPdfExporter();
			// exporter.setParameter(JRExporterParameter.JASPER_PRINT,
			// jasperPrint);
			// ByteArrayOutputStream baosReport = new ByteArrayOutputStream();
			// exporter.setParameter(JRExporterParameter.OUTPUT_STREAM,
			// baosReport);
			// exporter.exportReport();
			// byte[] toReturn = baosReport.toByteArray();
			// baosReport.close();

			// InputStream inputStream = new FileInputStream(toReturn.);

			// FacesContext facesContext = FacesContext.getCurrentInstance();
			// ExternalContext externalContext =
			// facesContext.getExternalContext();
			// externalContext.responseReset();
			// externalContext.setResponseContentType("application/pdf");
			// externalContext.setResponseContentLength(toReturn.length);
			// externalContext.setResponseHeader("Content-Disposition",
			// "attachment; filename=\"" + this.cpfSelecionado +
			// "-fichaAnualVinculosRendimentos.pdf");
			// OutputStream outputStream =
			// externalContext.getResponseOutputStream();
			// outputStream.write(toReturn);
			// outputStream.flush();
			// facesContext.responseComplete();

			// JasperExportManager.exportReportToPdfFile(jasperPrint,
			// this.cpfSelecionado +
			// "-fichaAnualVinculosFuncionaisRendimentos.pdf");
			//
			// JasperViewer jasperViewer = new JasperViewer(jasperPrint, false);
			// jasperViewer.setVisible(true);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	public void gerarRelatorioAcumulacaoVerbas() {
		String pathRelatorioDir = FacesContext.getCurrentInstance().getExternalContext()
				.getRealPath("relatorios/fichaFinanceira") + "/";
		String pathRelatorio = pathRelatorioDir + "relatorioFinanceiro.jasper";

		HashMap<String, Object> parameters = new LinkedHashMap<String, Object>();
		parameters.put("cpf", this.acumulacao.getCpf());
		parameters.put("usuario", this.loginBean.getUsuario().getNome());
		parameters.put("ano", this.acumulacao.getAno());
		parameters.put("REPORT_IMAGE_DIR", pathRelatorioDir);
		parameters.put("SUBREPORT_DIR", pathRelatorioDir);
		String competencia = this.acumulacao.getMes() + "-" + this.acumulacao.getAno();

		JasperPrint jasperPrint;
		try (Connection conexaoJasper = this.relatorioConexao.getConnection()) {
			HttpServletResponse httpServletResponse = (HttpServletResponse) FacesContext.getCurrentInstance()
					.getExternalContext().getResponse();
			httpServletResponse.addHeader("Content-disposition", "attachment; filename=" + "RelatorioAcumulacao-"
					+ this.acumulacao.getCpf() + "-" + competencia + ".pdf");
			ServletOutputStream arquivo = httpServletResponse.getOutputStream();

			
			jasperPrint = JasperFillManager.fillReport(pathRelatorio, parameters,
					conexaoJasper);
			JasperExportManager.exportReportToPdfStream(jasperPrint, arquivo);
			arquivo.flush();
			arquivo.close();
			
			FacesContext.getCurrentInstance().responseComplete();

			// JRExporter exporter = new JRPdfExporter();
			// exporter.setParameter(JRExporterParameter.JASPER_PRINT,
			// jasperPrint);
			// ByteArrayOutputStream baosReport = new ByteArrayOutputStream();
			// exporter.setParameter(JRExporterParameter.OUTPUT_STREAM,
			// baosReport);
			// exporter.exportReport();
			// byte[] toReturn = baosReport.toByteArray();
			// baosReport.close();

			// InputStream inputStream = new FileInputStream(toReturn.);

			// FacesContext facesContext = FacesContext.getCurrentInstance();
			// ExternalContext externalContext =
			// facesContext.getExternalContext();
			// externalContext.responseReset();
			// externalContext.setResponseContentType("application/pdf");
			// externalContext.setResponseContentLength(toReturn.length);
			// externalContext.setResponseHeader("Content-Disposition",
			// "attachment; filename=\"" + this.cpfSelecionado +
			// "-fichaAnualVinculosRendimentos.pdf");
			// OutputStream outputStream =
			// externalContext.getResponseOutputStream();
			// outputStream.write(toReturn);
			// outputStream.flush();
			// facesContext.responseComplete();

			// JasperExportManager.exportReportToPdfFile(jasperPrint,
			// this.cpfSelecionado +
			// "-fichaAnualVinculosFuncionaisRendimentos.pdf");
			//
			// JasperViewer jasperViewer = new JasperViewer(jasperPrint, false);
			// jasperViewer.setVisible(true);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	public void temAcumulacaoSelecionada() {
		if (this.acumulacoesSelecionadas == null || this.acumulacoesSelecionadas.isEmpty()) {
			Mensagem.setMensagem(MensagemType.ERRO, "Nenhuma Acumulação foi selecionada.", "");
		}
	}
	
//	@Transactional
//	public void iniciarAnalise() {
//		try {
//			this.analiseBusiness.iniciarAnalise(acumulacoesSelecionadas, this.iniciarAnaliseDespacho, this.loginBean.getUsuario());
//			
//			//Fecha o dialog após o processamento
////			RequestContext requestContext = RequestContext.getCurrentInstance();
////			requestContext.execute("PF('dlgInitAnlAcmVar').hide();");
//			PrimeFaces.current().executeScript("PF('dlgInitAnlAcmVar').hide();");
//			
//			// Limpa as acumulações selecionadas
////			requestContext.execute("PF('tblDistVar').unselectAllRows()");
//			PrimeFaces.current().executeScript("PF('tblDistVar').unselectAllRows();");
//	
//			//Mensagem de sucesso
//			Mensagem.setMensagem(MensagemType.INFORMACAO, "Análise de Acumulação", "A análise das acumulações seleciondas foi iniciada.");
//		} catch (Exception e) {
//			Mensagem.setMensagem(MensagemType.ERRO, e.getMessage(), "");
//		}
//		
//	}
	
	
	public boolean getPerfilDistribuicao() {
		
		Optional<UsuarioGrupo> usuarioGrupoDistribuicao = this.loginBean.getUsuario().getListaUsuarioGrupo().stream()
				.filter(ug -> ug.getAtivo() && ug.getGrupo().getAtivo()
						&& ug.getGrupo().getPerfil().equals(PerfilGrupo.DISTRIBUICAO))
				.findAny();
		
		if (usuarioGrupoDistribuicao.isPresent()) {
			return true;
		}
		return false;
	}

	public boolean getPerfilAuditoria() {
		Optional<UsuarioGrupo> usuarioGrupoAuditoria = this.loginBean.getUsuario().getListaUsuarioGrupo().stream()
				.filter(ug -> ug.getAtivo() && ug.getGrupo().getAtivo()
						&& ug.getGrupo().getPerfil().equals(PerfilGrupo.AUDITORIA))
				.findAny();
		if (usuarioGrupoAuditoria.isPresent()) {
			return true;
		}
		return false;
	}

	public Collection<Acumulacao> filtrarPorCargaHoraria(Collection<Acumulacao> acumulacoes, String filtro) {
    if (filtro == null || filtro.equals("0") || acumulacoes == null) {
        return acumulacoes;
    }

      return acumulacoes.stream()
        .filter(a -> {
            int chTotal = a.getCargaHorariaSemanalTotal();
            if ("1".equals(filtro)) {
                return chTotal > 60;
            } else if ("2".equals(filtro)) {
                return chTotal <= 60;
            }
            return true;
        })
        .collect(Collectors.toList());
}

}
