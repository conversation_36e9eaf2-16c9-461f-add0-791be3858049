package br.gov.ac.tce.sicapanalise.controle.bean.auditoria;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collection;

import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.Acumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.DetalhamentoAcumulacao;
import br.gov.ac.tce.sicapanalise.auditoria.business.AcumulacaoBusiness;
import br.gov.ac.tce.sicapanalise.auditoria.dto.ContraChequeDTO;
import br.gov.ac.tce.sicapweb.repositorio.ContraChequeRepositorio;

@Named
@ViewScoped
public class DetalhamentoAcumulacaoBean implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1960071683805133860L;

	@Inject
	private AcumulacaoBusiness acumulacaoBusiness;

	@Inject
	private ContraChequeRepositorio contraChequeRepositorio;

	private Long idAcumulacao;
	
//	private Collection<DetalhamentoAcumulacao> listaDetalhamentoAcumulacao;
	
	private Acumulacao acumulacao;
	
	public Long getIdAcumulacao() {
		return idAcumulacao;
	}

	public void setIdAcumulacao(Long idAcumulacao) {
		this.idAcumulacao = idAcumulacao;
		this.acumulacao = acumulacaoBusiness.retornaAcumulacaoPorId(this.idAcumulacao);
		carregarValoresLiquidos();
	}

//	public Collection<DetalhamentoAcumulacao> getListaDetalhamentoAcumulacao() {
//		return listaDetalhamentoAcumulacao;
//	}
//
//	public void setListaDetalhamentoAcumulacao(Collection<DetalhamentoAcumulacao> listaDetalhamentoAcumulacao) {
//		this.listaDetalhamentoAcumulacao = listaDetalhamentoAcumulacao;
//	}

	public Acumulacao getAcumulacao() {
		return acumulacao;
	}

	public void setAcumulacao(Acumulacao acumulacao) {
		this.acumulacao = acumulacao;
//		retornaAcumulacao();
	}

	public Integer getCargaHorariaSemanalTotal() {
		if (this.acumulacao != null && this.acumulacao.getListaDetalhamentoAcumulacao() != null) {
			return this.acumulacao.getListaDetalhamentoAcumulacao().stream()
					.mapToInt(detalhamento -> {
						Integer cargaSemanal = detalhamento.getCargaHorariaSemanal();
						return cargaSemanal != null ? cargaSemanal : 0;
					})
					.sum();
		}
		return 0;
	}

	private void carregarValoresLiquidos() {
		if (this.acumulacao != null && this.acumulacao.getListaDetalhamentoAcumulacao() != null) {
			for (DetalhamentoAcumulacao detalhamento : this.acumulacao.getListaDetalhamentoAcumulacao()) {
				Collection<ContraChequeDTO> contraCheques = contraChequeRepositorio.retornaContraCheque(
					detalhamento.getBeneficiario().getId(),
					detalhamento.getAno(),
					detalhamento.getMes().getNumero()
				);

				if (contraCheques != null && !contraCheques.isEmpty()) {
					ContraChequeDTO contraCheque = contraCheques.iterator().next();
					BigDecimal valorLiquido = contraCheque.getTotalVencimentos().subtract(contraCheque.getTotalDescontos());
					detalhamento.setTotalLiquido(valorLiquido);
				}
			}
		}
	}

//	private Acumulacao retornaAcumulacao() {
//		if (listaDetalhamentoAcumulacao == null || listaDetalhamentoAcumulacao.isEmpty())
//			return null;
//		return listaDetalhamentoAcumulacao.iterator().next().getAcumulacao();
//	}

}
