package br.gov.ac.tce.sicapanalise.auditoria.repositorio;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Fetch;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.hibernate.Criteria;

import javax.persistence.criteria.Expression;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.Acumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.AnaliseAcumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.DetalhamentoAcumulacao;
import br.gov.ac.tce.sicapanalise.auditoria.dto.EntidadeAcumulacaoDTO;
import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;

@Stateless
public class AcumulacaoRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public void atualizar(Acumulacao acumulacao) {
		entityManager.merge(acumulacao);
	}

	@SuppressWarnings("unchecked")
	public Collection<Object[]> listaCompetencias() throws RepositorioException {
		Collection<Object[]> listaCompetencias = null;

		try {
			Query query = this.entityManager.createNativeQuery("SELECT a.ano, a.mes, " + "CASE a.mes "
					+ "WHEN 1 THEN 'Janeiro' " + "WHEN 2 THEN 'Fevereiro' " + "WHEN 3 THEN 'Março' "
					+ "WHEN 4 THEN 'Abril' " + "WHEN 5 THEN 'Maio' " + "WHEN 6 THEN 'Junho' " + "WHEN 7 THEN 'Julho' "
					+ "WHEN 8 THEN 'Agosto' " + "WHEN 9 THEN 'Setembro' " + "WHEN 10 THEN 'Outubro' "
					+ "WHEN 11 THEN 'Novembro' " + "WHEN 12 THEN 'Dezembro' " + "ELSE 'Nenhum' END AS nomeMes "
					+ "FROM auditoria.acumulacao a " + "GROUP BY a.ano, a.mes ORDER BY a.ano DESC, a.mes DESC");

			listaCompetencias = query.getResultList();

		} catch (Exception e) {
			throw new RepositorioException("Erro acumulacaoRepositorio.listaCompetencias.", e.getCause());
		}
		return listaCompetencias;
	}

	@SuppressWarnings("unchecked")
	public Collection<Object[]> listaAcumulacoes(String competencia, String cpf, String situacaoAcumulacao, String nome,
			Integer idEntidadeCjur, Integer idCargo) throws RepositorioException {
		Collection<Object[]> listaAcumulacoes = null;

		String competenciaSplit[];
		String sql;
		List<String> condicoes = new LinkedList<>();
		String where = "";

		try {

			sql = "SELECT a.id, a.cpf, a.nome, a.ano, a.mes, a.nomeMes, a.situacao, COUNT(1) as qtd, a.textoSituacao "
					+ "FROM auditoria.vw_lista_acumulacoes a "
					+ "INNER JOIN auditoria.DetalhamentoAcumulacao d ON a.id = d.idAcumulacao";

			if (!competencia.equals("0")) {
				competenciaSplit = competencia.split("/");
				condicoes.add("a.mes = " + competenciaSplit[0]);
				condicoes.add("a.ano = " + competenciaSplit[1]);
			}

			if (!cpf.equals("")) {
				condicoes.add("a.cpf like '%" + cpf + "%'");
			}

			if (!nome.equals("")) {
				condicoes.add("a.nome like '%" + nome + "%'");
			}

			if (!situacaoAcumulacao.equals("0")) {
				condicoes.add("a.situacao = '" + situacaoAcumulacao + "'");
			}

			if (idEntidadeCjur != 0) {
				condicoes.add("(exists (select 1 from auditoria.DetalhamentoAcumulacao d2 where d2.idEntidadeCjur = "
						+ idEntidadeCjur + " and a.id = d2.idAcumulacao) )");
			}

			if (idCargo != 0) {
				condicoes.add("d.idCargo = " + idCargo);
			} else {
				if (idCargo == 9999999) {
					condicoes.add("d.idCargo IS NULL");
				}
			}

			if (condicoes.size() != 0) {
				where = " where " + String.join(" AND ", condicoes);
			}

			sql += where
					+ " GROUP BY a.id, a.cpf, a.nome, a.ano, a.mes, a.nomeMes, a.situacao, a.textoSituacao ORDER BY a.nome";

			System.out.println(sql);

			Query query = this.entityManager.createNativeQuery(sql);

			listaAcumulacoes = query.getResultList();

		} catch (Exception e) {
			e.printStackTrace();
			throw new RepositorioException("Erro acumulacaoRepositorio.listaAcumulacoes.", e.getCause());
		}
		return listaAcumulacoes;
	}

	@SuppressWarnings("unchecked")
	public Collection<Object[]> detalharAcumulacoes(Long idAcumulacao) throws RepositorioException {
		Collection<Object[]> listarDetalhesAcumulacao = null;

		try {
			Query query = this.entityManager.createNativeQuery("SELECT e.nome as entidadeCjur, "
					+ "ISNULL(c.nome, 'INDEFINIDO') AS nomeCargo, "
					+ "ISNULL(c.cargaHorariaMensal, 0) AS cargaHorariaMensal, "
					+ "CASE c.tipo WHEN 1 THEN 'Efetivo' WHEN 2 THEN 'Comissionado' "
					+ "WHEN 3 THEN 'Temporário' ELSE 'Indefinido' END as tipoCargo, "
					+ "ISNULL(m.nome, 'INDEFINIDO') AS municipioLotacao " + "FROM auditoria.DetalhamentoAcumulacao d "
					+ "LEFT JOIN Entidade e ON e.idEntidadeCjur = d.idEntidadeCjur "
					+ "LEFT JOIN Cargo c ON d.idCargo = c.id " + "LEFT JOIN Municipio m ON m.id = d.idMunicipio "
					+ "WHERE d.idAcumulacao = ? " + "ORDER BY e.nome");

			query.setParameter(1, idAcumulacao);

			listarDetalhesAcumulacao = query.getResultList();

		} catch (Exception e) {
			e.printStackTrace();
			throw new RepositorioException("Erro acumulacaoRepositorio.detalharAcumulacoes.", e.getCause());
		}
		return listarDetalhesAcumulacao;
	}

	@SuppressWarnings("unchecked")
	public Collection<Object[]> listaCargosAcumulacao(String competencia, Integer idEntidadeCjur)
			throws RepositorioException {
		Collection<Object[]> listarCargosAcumulacao = null;

		try {

			String competenciaSplit[];
			String sql;
			List<String> condicoes = new LinkedList<>();
			String where = "";

			sql = "SELECT " + "CASE WHEN da.idCargo IS NULL THEN 9999999 ELSE da.idCargo END AS idCargo, "
					+ "CASE WHEN c.nome IS NULL THEN 'INDEFINIDO' ELSE c.nome END AS nome "
					+ "FROM auditoria.DetalhamentoAcumulacao da " + "LEFT JOIN Cargo c ON da.idCargo = c.id ";

			if (competencia != null && !competencia.equals("0")) {
				competenciaSplit = competencia.split("/");
				condicoes.add("da.mes = " + competenciaSplit[0]);
				condicoes.add("da.ano = " + competenciaSplit[1]);
			}

			if (idEntidadeCjur != 0) {
				condicoes.add("da.idEntidadeCjur = " + idEntidadeCjur);
			}

			if (condicoes.size() != 0) {
				where = " where " + String.join(" AND ", condicoes);
			}

			sql += where + " GROUP BY da.idCargo, c.nome ORDER BY c.nome";

			Query query = this.entityManager.createNativeQuery(sql);
			listarCargosAcumulacao = query.getResultList();

		} catch (Exception e) {
			e.printStackTrace();
			throw new RepositorioException("Erro acumulacaoRepositorio.listaCargosAcumulacao.", e.getCause());
		}
		return listarCargosAcumulacao;
	}

	@SuppressWarnings("unchecked")
	public Collection<Object[]> listaCargosAcumulacao(List<String> competencias, Integer idEntidadeCjur) throws RepositorioException {
		Collection<Object[]> listarCargosAcumulacao = null;

		String sql;
		List<String> condicoes = new LinkedList<>();
		String where = "";

		try {
			System.out.println("=== INICIANDO listaCargosAcumulacao ===");
			System.out.println("Competências recebidas: " + competencias);
			System.out.println("Entidade: " + idEntidadeCjur);

			sql = "SELECT da.idCargo, c.nome FROM auditoria.DetalhamentoAcumulacao da "
					+ "INNER JOIN Cargo c ON da.idCargo = c.id "
					+ "INNER JOIN auditoria.Acumulacao a ON da.idAcumulacao = a.id";

			if (competencias != null && !competencias.isEmpty()) {
				System.out.println("Processando " + competencias.size() + " competências para cargos...");
				List<String> competenciaCondicoes = new ArrayList<>();
				for (String competencia : competencias) {
					System.out.println("Processando competência para cargos: " + competencia);
					if (competencia != null && !competencia.equals("0")) {
						String[] competenciaSplit = competencia.split("/");
						String condicao = "(a.mes = " + competenciaSplit[0] + " AND a.ano = " + competenciaSplit[1] + ")";
						competenciaCondicoes.add(condicao);
						System.out.println("Condição adicionada: " + condicao);
					}
				}
				if (!competenciaCondicoes.isEmpty()) {
					String condicaoFinal = "(" + String.join(" OR ", competenciaCondicoes) + ")";
					condicoes.add(condicaoFinal);
					System.out.println("Condição final de competências: " + condicaoFinal);
				}
			}

			if (idEntidadeCjur != null && idEntidadeCjur > 0) {
				condicoes.add("da.idEntidadeCjur = " + idEntidadeCjur);
			}

			if (!condicoes.isEmpty()) {
				where = " WHERE " + String.join(" AND ", condicoes);
			}

			sql += where + " GROUP BY da.idCargo, c.nome ORDER BY c.nome";

			System.out.println("=== CONSULTA SQL FINAL PARA CARGOS ===");
			System.out.println(sql);

			// Teste adicional: verificar se há dados na tabela DetalhamentoAcumulacao
			if (competencias != null && !competencias.isEmpty()) {
				String competencia = competencias.get(0);
				String[] competenciaSplit = competencia.split("/");
				Integer mes = Integer.valueOf(competenciaSplit[0]);
				Integer ano = Integer.valueOf(competenciaSplit[1]);

				Query testQuery = this.entityManager.createNativeQuery(
					"SELECT COUNT(*) FROM auditoria.DetalhamentoAcumulacao da " +
					"INNER JOIN auditoria.Acumulacao a ON da.idAcumulacao = a.id " +
					"WHERE a.mes = ? AND a.ano = ?");
				testQuery.setParameter(1, mes);
				testQuery.setParameter(2, ano);
				Object totalDetalhamento = testQuery.getSingleResult();
				System.out.println("Total de DetalhamentoAcumulacao para " + mes + "/" + ano + ": " + totalDetalhamento);

				// Teste para verificar cargos únicos
				Query testCargos = this.entityManager.createNativeQuery(
					"SELECT COUNT(DISTINCT da.idCargo) FROM auditoria.DetalhamentoAcumulacao da " +
					"INNER JOIN auditoria.Acumulacao a ON da.idAcumulacao = a.id " +
					"WHERE a.mes = ? AND a.ano = ? AND da.idCargo IS NOT NULL");
				testCargos.setParameter(1, mes);
				testCargos.setParameter(2, ano);
				Object totalCargosUnicos = testCargos.getSingleResult();
				System.out.println("Total de cargos únicos para " + mes + "/" + ano + ": " + totalCargosUnicos);
			}

			Query query = this.entityManager.createNativeQuery(sql);
			listarCargosAcumulacao = query.getResultList();

			System.out.println("=== RESULTADO DA CONSULTA DE CARGOS ===");
			System.out.println("Total de cargos encontrados: " + (listarCargosAcumulacao != null ? listarCargosAcumulacao.size() : "null"));

			if (listarCargosAcumulacao != null && !listarCargosAcumulacao.isEmpty()) {
				System.out.println("Primeiros 5 cargos encontrados:");
				int count = 0;
				for (Object[] cargo : listarCargosAcumulacao) {
					if (count >= 5) break;
					System.out.println("  Cargo ID: " + cargo[0] + ", Nome: " + cargo[1]);
					count++;
				}
			}

		} catch (Exception e) {
			System.out.println("ERRO na consulta de cargos: " + e.getMessage());
			e.printStackTrace();
			throw new RepositorioException("Erro acumulacaoRepositorio.listaCargosAcumulacao.", e.getCause());
		}
		return listarCargosAcumulacao;
	}

	public Collection<Acumulacao> listaAcumulacoes2(String competencia, String situacao, String cpf, String nome,
			Integer entidade, Integer cargo) throws RepositorioException {
		return listaAcumulacoes2(competencia, situacao, cpf, nome, entidade, cargo, 0, 1000);
	}

	public Collection<Acumulacao> listaAcumulacoes2(String competencia, String situacao, String cpf, String nome,
			Integer entidade, Integer cargo, int firstResult, int maxResults) throws RepositorioException {
		Collection<Acumulacao> listaAcumulacoes = null;

		try {

			CriteriaBuilder cb = entityManager.getCriteriaBuilder();
			CriteriaQuery<Acumulacao> query = cb.createQuery(Acumulacao.class);

			Root<Acumulacao> root = query.from(Acumulacao.class);
			Join<Acumulacao, DetalhamentoAcumulacao> detalhamentoJoin = root.join("listaDetalhamentoAcumulacao");
			root.join("listaDistribuicaoAcumulacao", JoinType.LEFT);

			List<Predicate> condicoes = new ArrayList<Predicate>();

			if (competencia != null && !competencia.equals("0")) {
				Integer mes = Integer.valueOf(competencia.split("/")[0]);
				Integer ano = Integer.valueOf(competencia.split("/")[1]);

				Predicate filtroMes = cb.equal(root.<Integer>get("mes"), mes);
				Predicate filtroAno = cb.equal(root.<Integer>get("ano"), ano);

				condicoes.add(filtroMes);
				condicoes.add(filtroAno);
			}

			if (situacao != null) {
				Predicate filtroSituacao = null;
				if (!situacao.isEmpty()) {
					filtroSituacao = cb.equal(root.<String>get("situacao"), situacao);
				} else {
					filtroSituacao = cb.isNull(root.<String>get("situacao"));
				}
				condicoes.add(filtroSituacao);
			}

			if (cpf != null && !cpf.isEmpty()) {
				String cpfSemMascara = cpf.replaceAll("[^0-9]", "");
				Predicate filtroCpf = cb.like(root.get("cpf"), "%".concat(cpfSemMascara).concat("%"));
				condicoes.add(filtroCpf);
			}

			if (nome != null && !nome.isEmpty()) {
				Predicate filtroNome = cb.like(cb.upper(root.get("nome")), "%".concat(nome.toUpperCase().concat("%")));
				condicoes.add(filtroNome);
			}

			if (entidade != null && entidade > 0) {
				Predicate filtroEntidade = cb.equal(detalhamentoJoin.get("entidade"), entidade);
				condicoes.add(filtroEntidade);
			}

			if (cargo != null && cargo > 0) {
				Predicate filtroCargo = cb.equal(detalhamentoJoin.get("cargo"), cargo);
				condicoes.add(filtroCargo);
			}

			query.where((Predicate[]) condicoes.toArray(new Predicate[0])).distinct(true);

			TypedQuery<Acumulacao> typedQuery = entityManager.createQuery(query);

			if (maxResults > 0) {
				typedQuery.setFirstResult(firstResult);
				typedQuery.setMaxResults(maxResults);
			}

			listaAcumulacoes = typedQuery.getResultList();

		} catch (Exception e) {
			e.printStackTrace();
			throw new RepositorioException("Erro acumulacaoRepositorio.listaAcumulacoes2.", e.getCause());
		}
		return listaAcumulacoes;
	}

	public Collection<Acumulacao> listaAcumulacoes2(String competencia, String situacao, String cpf, String nome,
			Integer entidade, Integer cargo, String cargaHorariaSemanalFiltro, String numeroVinculosFiltro) throws RepositorioException {
		return listaAcumulacoes2(competencia, situacao, cpf, nome, entidade, cargo, cargaHorariaSemanalFiltro, numeroVinculosFiltro, 0, 1000);
	}

	public Collection<Acumulacao> listaAcumulacoes2(String competencia, String situacao, String cpf, String nome,
			Integer entidade, Integer cargo, String cargaHorariaSemanalFiltro, String numeroVinculosFiltro,
			int firstResult, int maxResults) throws RepositorioException {
		Collection<Acumulacao> listaAcumulacoes = null;

		try {
			System.out.println("Iniciando listaAcumulacoes2 com parametros:");
			System.out.println("competencia: " + competencia);
			System.out.println("situacao: " + situacao);
			System.out.println("cpf: " + cpf);
			System.out.println("nome: " + nome);
			System.out.println("entidade: " + entidade);
			System.out.println("cargo: " + cargo);
			System.out.println("firstResult: " + firstResult);
			System.out.println("maxResults: " + maxResults);

			CriteriaBuilder cb = entityManager.getCriteriaBuilder();
			CriteriaQuery<Acumulacao> query = cb.createQuery(Acumulacao.class);

			Root<Acumulacao> root = query.from(Acumulacao.class);
			Join<Acumulacao, DetalhamentoAcumulacao> detalhamentoJoin = root.join("listaDetalhamentoAcumulacao");
			root.join("listaDistribuicaoAcumulacao", JoinType.LEFT);

			List<Predicate> condicoes = new ArrayList<Predicate>();

			if (competencia != null && !competencia.equals("0")) {
				Integer mes = Integer.valueOf(competencia.split("/")[0]);
				Integer ano = Integer.valueOf(competencia.split("/")[1]);

				Predicate filtroMes = cb.equal(root.<Integer>get("mes"), mes);
				Predicate filtroAno = cb.equal(root.<Integer>get("ano"), ano);

				condicoes.add(filtroMes);
				condicoes.add(filtroAno);
			}

			if (situacao != null) {
				Predicate filtroSituacao = null;
				if (!situacao.isEmpty()) {
					filtroSituacao = cb.equal(root.<String>get("situacao"), situacao);
				} else {
					filtroSituacao = cb.isNull(root.<String>get("situacao"));
				}
				condicoes.add(filtroSituacao);
			}

			if (cpf != null && !cpf.isEmpty()) {
				String cpfSemMascara = cpf.replaceAll("[^0-9]", "");
				Predicate filtroCpf = cb.like(root.get("cpf"), "%".concat(cpfSemMascara).concat("%"));
				condicoes.add(filtroCpf);
			}

			if (nome != null && !nome.isEmpty()) {
				Predicate filtroNome = cb.like(cb.upper(root.get("nome")), "%".concat(nome.toUpperCase().concat("%")));
				condicoes.add(filtroNome);
			}

			if (entidade != null && entidade > 0) {
				Predicate filtroEntidade = cb.equal(detalhamentoJoin.get("entidade"), entidade);
				condicoes.add(filtroEntidade);
			}

			if (cargo != null && cargo > 0) {
				Predicate filtroCargo = cb.equal(detalhamentoJoin.get("cargo"), cargo);
				condicoes.add(filtroCargo);
			}

			if (cargaHorariaSemanalFiltro != null && !cargaHorariaSemanalFiltro.equals("0")) {
				
				Expression<Integer> cargaHorariaTotal = root.get("cargaHorariaTotal");
				
				Expression<Integer> divisor = cb.literal(4);
				
				Expression<Integer> chSemanal = cb.toInteger(cb.quot(cargaHorariaTotal, divisor));
				
				Expression<Integer> sessenta = cb.literal(60);
				
				Predicate filtroCH = cargaHorariaSemanalFiltro.equals("1")
					? cb.greaterThan(chSemanal, sessenta)  // >60
					: cb.lessThanOrEqualTo(chSemanal, sessenta); // ≤60
				
				condicoes.add(filtroCH);
			}

			if (numeroVinculosFiltro != null && !numeroVinculosFiltro.equals("0")) {
				if (numeroVinculosFiltro.equals("2")) {
					Predicate filtroVinculos = cb.equal(root.<Integer>get("quantidadeVinculos"), 2);
					condicoes.add(filtroVinculos);
				} else if (numeroVinculosFiltro.equals("3")) { 
					Predicate filtroVinculos = cb.greaterThanOrEqualTo(root.<Integer>get("quantidadeVinculos"), 3);
					condicoes.add(filtroVinculos);
				}
			}

			query.where((Predicate[]) condicoes.toArray(new Predicate[0])).distinct(true);

			TypedQuery<Acumulacao> typedQuery = entityManager.createQuery(query);

			if (maxResults > 0) {
				typedQuery.setFirstResult(firstResult);
				typedQuery.setMaxResults(maxResults);
			}

			listaAcumulacoes = typedQuery.getResultList();

		} catch (Exception e) {
			e.printStackTrace();
			throw new RepositorioException("Erro acumulacaoRepositorio.listaAcumulacoes2.", e.getCause());
		}
		return listaAcumulacoes;
	}

	public Collection<Acumulacao> listaAcumulacoes2(List<String> competencias, String situacao, String cpf, String nome,
			Integer entidade, Integer cargo, String cargaHorariaSemanalFiltro, String numeroVinculosFiltro) throws RepositorioException {
		return listaAcumulacoes2(competencias, situacao, cpf, nome, entidade, cargo, cargaHorariaSemanalFiltro, numeroVinculosFiltro, 0, 1000);
	}

	public Collection<Acumulacao> listaAcumulacoes2(List<String> competencias, String situacao, String cpf, String nome,
			Integer entidade, Integer cargo, String cargaHorariaSemanalFiltro, String numeroVinculosFiltro,
			int firstResult, int maxResults) throws RepositorioException {
		Collection<Acumulacao> listaAcumulacoes = null;

		try {
			System.out.println("=== INICIANDO listaAcumulacoes2 com MÚLTIPLAS COMPETÊNCIAS ===");
			System.out.println("Competências recebidas: " + competencias);
			System.out.println("Situação: " + situacao);
			System.out.println("CPF: " + cpf);
			System.out.println("Nome: " + nome);
			System.out.println("Entidade: " + entidade);
			System.out.println("Cargo: " + cargo);

			System.out.println("Limpando cache do EntityManager...");
			entityManager.clear();

			CriteriaBuilder cb = entityManager.getCriteriaBuilder();
			CriteriaQuery<Acumulacao> query = cb.createQuery(Acumulacao.class);

			Root<Acumulacao> root = query.from(Acumulacao.class);

			Join<Acumulacao, DetalhamentoAcumulacao> detalhamentoJoin = null;
			if (entidade != null && entidade > 0 || cargo != null && cargo > 0) {
				System.out.println("Adicionando JOIN com DetalhamentoAcumulacao devido a filtros de entidade/cargo");
				detalhamentoJoin = root.join("listaDetalhamentoAcumulacao");
			}
			// Comentando temporariamente o JOIN com DistribuicaoAcumulacao
			// root.join("listaDistribuicaoAcumulacao", JoinType.LEFT);

			List<Predicate> condicoes = new ArrayList<Predicate>();

			if (competencias != null && !competencias.isEmpty()) {
				System.out.println("Processando " + competencias.size() + " competências...");
				List<Predicate> competenciaPredicates = new ArrayList<>();
				for (String competencia : competencias) {
					System.out.println("Processando competência: " + competencia);
					if (competencia != null && !competencia.equals("0")) {
						String[] competenciaSplit = competencia.split("/");
						Integer mes = Integer.valueOf(competenciaSplit[0]);
						Integer ano = Integer.valueOf(competenciaSplit[1]);
						System.out.println("Mês: " + mes + ", Ano: " + ano);

						

						Predicate filtroMes = cb.equal(root.<Integer>get("mes"), mes);
						Predicate filtroAno = cb.equal(root.<Integer>get("ano"), ano);

						competenciaPredicates.add(cb.and(filtroMes, filtroAno));
					}
				}
				if (!competenciaPredicates.isEmpty()) {
					System.out.println("Adicionando " + competenciaPredicates.size() + " predicados de competência");
					condicoes.add(cb.or(competenciaPredicates.toArray(new Predicate[0])));
				} else {
					System.out.println("AVISO: Nenhum predicado de competência foi criado!");
				}
			} else {
				System.out.println("AVISO: Lista de competências está vazia ou nula!");
			}

			if (cpf != null && !cpf.equals("")) {
				Predicate filtroCpf = cb.like(root.<String>get("cpf"), "%" + cpf + "%");
				condicoes.add(filtroCpf);
			}

			if (nome != null && !nome.equals("")) {
				Predicate filtroNome = cb.like(root.<String>get("nome"), "%" + nome + "%");
				condicoes.add(filtroNome);
			}

			if (situacao != null && !situacao.equals("0")) {
				Predicate filtroSituacao = cb.equal(root.<String>get("situacao"), situacao);
				condicoes.add(filtroSituacao);
			}

			if (entidade != null && entidade > 0 && detalhamentoJoin != null) {
				System.out.println("Aplicando filtro de entidade: " + entidade);
				Predicate filtroEntidade = cb.equal(detalhamentoJoin.get("entidade"), entidade);
				condicoes.add(filtroEntidade);
			}

			if (cargo != null && cargo > 0 && detalhamentoJoin != null) {
				System.out.println("Aplicando filtro de cargo: " + cargo);
				Predicate filtroCargo = cb.equal(detalhamentoJoin.get("cargo"), cargo);
				condicoes.add(filtroCargo);
			}

			if (numeroVinculosFiltro != null && !numeroVinculosFiltro.equals("0")) {
				if (numeroVinculosFiltro.equals("2")) {
					Predicate filtroVinculos = cb.equal(root.<Integer>get("quantidadeVinculos"), 2);
					condicoes.add(filtroVinculos);
				} else if (numeroVinculosFiltro.equals("3")) {
					Predicate filtroVinculos = cb.greaterThanOrEqualTo(root.<Integer>get("quantidadeVinculos"), 3);
					condicoes.add(filtroVinculos);
				}
			}

			query.where((Predicate[]) condicoes.toArray(new Predicate[0])).distinct(true);

			TypedQuery<Acumulacao> typedQuery = entityManager.createQuery(query);

			if (maxResults > 0) {
				typedQuery.setFirstResult(firstResult);
				typedQuery.setMaxResults(maxResults);
			}

			System.out.println("=== CONSULTA GERADA ===");
			System.out.println("Total de condições aplicadas: " + condicoes.size());
			System.out.println("Paginação aplicada - firstResult: " + firstResult + ", maxResults: " + maxResults);

			listaAcumulacoes = typedQuery.getResultList();

			System.out.println("=== RESULTADO DA CONSULTA ===");
			System.out.println("Total de acumulações encontradas: " + (listaAcumulacoes != null ? listaAcumulacoes.size() : "null"));

		} catch (Exception e) {
			System.out.println("ERRO na consulta de acumulações: " + e.getMessage());
			e.printStackTrace();
			throw new RepositorioException("Erro acumulacaoRepositorio.listaAcumulacoes2.", e.getCause());
		}
		return listaAcumulacoes;
	}

	public Acumulacao detalharAcumulacao2(Acumulacao servidor) throws RepositorioException {
		Acumulacao acumulacaoDetalhes = null;
		try {

			CriteriaBuilder cb = entityManager.getCriteriaBuilder();
			CriteriaQuery<Acumulacao> query = cb.createQuery(Acumulacao.class);

			Root<Acumulacao> root = query.from(Acumulacao.class);

			Fetch<Acumulacao, DetalhamentoAcumulacao> vinculosFetch = root.fetch("listaDetalhamentoAcumulacao");

			vinculosFetch.fetch("entidade");
			vinculosFetch.fetch("beneficiario");
			vinculosFetch.fetch("municipio");
			vinculosFetch.fetch("cargo", JoinType.LEFT).fetch("referenciaSub", JoinType.LEFT);

			query.where(cb.equal(root, servidor)).distinct(true);

			TypedQuery<Acumulacao> typedQuery = entityManager.createQuery(query);

			acumulacaoDetalhes = typedQuery.getSingleResult();

		} catch (Exception e) {
			e.printStackTrace();
			throw new RepositorioException("Erro acumulacaoRepositorio.detalharAcumulacao2.", e.getCause());
		}
		return acumulacaoDetalhes;
	}

	public Collection<DetalhamentoAcumulacao> listaVinculos(String competencia, Integer entidade, String cpf)
			throws RepositorioException {
		Collection<DetalhamentoAcumulacao> listaVinculos = null;

		try {
			CriteriaBuilder cb = entityManager.getCriteriaBuilder();
			CriteriaQuery<DetalhamentoAcumulacao> query = cb.createQuery(DetalhamentoAcumulacao.class);

			Root<DetalhamentoAcumulacao> root = query.from(DetalhamentoAcumulacao.class);
			root.fetch("cargo", JoinType.LEFT).fetch("referenciaSub", JoinType.LEFT);
			root.fetch("acumulacao");
			root.fetch("beneficiario");
			root.fetch("municipio");

			List<Predicate> condicoes = new ArrayList<Predicate>();

			if (competencia != null && !competencia.equals("0")) {
				Integer mes = Integer.valueOf(competencia.split("/")[0]);
				Integer ano = Integer.valueOf(competencia.split("/")[1]);

				Predicate filtroMes = cb.equal(root.<Integer>get("mes"), mes);
				Predicate filtroAno = cb.equal(root.<Integer>get("ano"), ano);

				condicoes.add(filtroMes);
				condicoes.add(filtroAno);
			}

			if (cpf != null && !cpf.isEmpty()) {
				String cpfSemMascara = cpf.replaceAll("[^0-9]", "");
				Predicate filtroCpf = cb.like(root.get("cpf"), "%".concat(cpfSemMascara).concat("%"));
				condicoes.add(filtroCpf);
			}

			if (entidade != null && entidade > 0) {
				Predicate filtroEntidade = cb.equal(root.get("entidade"), entidade);
				condicoes.add(filtroEntidade);
			}

			query.where((Predicate[]) condicoes.toArray(new Predicate[0]));

			TypedQuery<DetalhamentoAcumulacao> typedQuery = entityManager.createQuery(query);

			listaVinculos = typedQuery.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
			throw new RepositorioException("Erro acumulacaoRepositorio.listaVinculos.", e.getCause());
		}
		return listaVinculos;
	}

	public Collection<EntidadeAcumulacaoDTO> listaEntidadesAcumulacoes(String competencia, String cpf)
			throws RepositorioException {
		Collection<EntidadeAcumulacaoDTO> listaEntidadesAcumulacoes = null;

		try {

			CriteriaBuilder cb = entityManager.getCriteriaBuilder();
			CriteriaQuery<EntidadeAcumulacaoDTO> query = cb.createQuery(EntidadeAcumulacaoDTO.class);

			Root<DetalhamentoAcumulacao> root = query.from(DetalhamentoAcumulacao.class);
			Join<DetalhamentoAcumulacao, Entidade> entidadeJoin = root.join("entidade");

			List<Predicate> condicoes = new ArrayList<Predicate>();

			if (competencia != null && !competencia.equals("0")) {
				Integer mes = Integer.valueOf(competencia.split("/")[0]);
				Integer ano = Integer.valueOf(competencia.split("/")[1]);

				Predicate filtroMes = cb.equal(root.<Integer>get("mes"), mes);
				Predicate filtroAno = cb.equal(root.<Integer>get("ano"), ano);

				condicoes.add(filtroMes);
				condicoes.add(filtroAno);
			}

			if (cpf != null && !cpf.isEmpty()) {
				String cpfSemMascara = cpf.replaceAll("[^0-9]", "");
				Predicate filtroCpf = cb.like(root.get("cpf"), "%".concat(cpfSemMascara).concat("%"));
				condicoes.add(filtroCpf);
			}

			query.where((Predicate[]) condicoes.toArray(new Predicate[0]));

			query.select(cb.construct(EntidadeAcumulacaoDTO.class, entidadeJoin.<Integer>get("idEntidadeCjur"),
					entidadeJoin.get("nome"), cb.count(root)))
					.groupBy(entidadeJoin.<Integer>get("idEntidadeCjur"), entidadeJoin.get("nome"));

			listaEntidadesAcumulacoes = entityManager.createQuery(query).getResultList();

		} catch (Exception e) {
			throw new RepositorioException("Erro acumulacaoRepositorio.listaAcumulacoesEntidades.", e.getCause());
		}
		return listaEntidadesAcumulacoes;
	}

	public Collection<AnaliseAcumulacao> listaAcumulacoesNotificadas() {
		Collection<AnaliseAcumulacao> listaAcumulacoesNotificadas = null;
		String jpql = "SELECT DISTINCT aa FROM AnaliseAcumulacao aa " + "JOIN FETCH aa.acumulacao ac "
				+ "JOIN FETCH aa.analise an " + "JOIN FETCH an.usuario u " + "JOIN an.listaNotificacao";
		try {
			listaAcumulacoesNotificadas = entityManager.createQuery(jpql, AnaliseAcumulacao.class).getResultList();
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println(e.getMessage());
		}
		return listaAcumulacoesNotificadas;
	}

//	public Acumulacao retornaDetalhamentoAcumulacao(Long idAcumulacao) {
////		Collection<DetalhamentoAcumulacao> listaDetalhamentoAcumulacoes = null;
//		Acumulacao acumulacao = null;
//		String jpql = "SELECT a FROM Acumulacao a " + "JOIN FETCH a.listaDetalhamentoAcumulacao da "
//				+ "JOIN FETCH da.entidade " + "LEFT JOIN FETCH da.municipio " + "JOIN FETCH da.beneficiario b "
//				+ "LEFT JOIN FETCH da.cargo c " + "LEFT JOIN FETCH c.referenciaSub " + "WHERE a.id = :pId";
//		try {
//			acumulacao = entityManager.createQuery(jpql, Acumulacao.class).setParameter("pId", idAcumulacao)
//					.getSingleResult();
//		} catch (Exception e) {
//			e.printStackTrace();
//			System.out.println(e.getMessage());
//		}
//		return acumulacao;
//	}

	public Collection<Acumulacao> listaAcumulacoes(List<Long> acumulacaoIds) {
		Collection<Acumulacao> listaAcumulacoes = null;
		String jpql = "select a from Acumulacao a where id in :pIds";
		try {
			listaAcumulacoes = entityManager.createQuery(jpql, Acumulacao.class).setParameter("pIds", acumulacaoIds)
					.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println(e.getMessage());
		}

		return listaAcumulacoes;
	}

	public Acumulacao retornaAcumulacaoPorId(Long acumulacaoId) {
		Acumulacao acumulacao = null;
		String jpql = "SELECT a FROM Acumulacao a " + "JOIN FETCH a.listaDetalhamentoAcumulacao da "
				+ "JOIN FETCH da.entidade " + "LEFT JOIN FETCH da.municipio " + "LEFT JOIN FETCH da.cargo c "
				+ "LEFT JOIN FETCH c.referenciaSub " + "JOIN FETCH da.beneficiario b " + "JOIN FETCH b.cadastroUnico "
				+ "JOIN FETCH b.remessaEventual " + "WHERE a.id = :pId";
		try {
			acumulacao = entityManager.createQuery(jpql, Acumulacao.class).setParameter("pId", acumulacaoId)
					.getSingleResult();
		} catch (javax.persistence.NoResultException e) {
			System.out.println("Nenhuma acumulação encontrada para o ID: " + acumulacaoId);
			return null;
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println("Erro ao buscar acumulação por ID " + acumulacaoId + ": " + e.getMessage());
		}
		return acumulacao;
	}

	public Acumulacao retornaAcumulacaoPorAnalise(Long analiseId) {
		Acumulacao acumulacao = null;
		String jpql = "select distinct a from Acumulacao a JOIN FETCH a.listaAnaliseAcumulacao aa where aa.analise.id = :pId";
		try {
			acumulacao = entityManager.createQuery(jpql, Acumulacao.class).setParameter("pId", analiseId)
					.getSingleResult();
		} catch (javax.persistence.NoResultException e) {
			System.out.println("Nenhuma acumulação encontrada para a análise ID: " + analiseId);
			return null;
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println("Erro ao buscar acumulação por análise ID " + analiseId + ": " + e.getMessage());
		}
		return acumulacao;
	}

	@SuppressWarnings("unchecked")
	public Collection<Object[]> resumoConcomitanciaPorCpf(String cpf) throws RepositorioException {
		try {
			String sql = "SELECT a.cpf, a.nome, " +
						 "STRING_AGG(CONVERT(VARCHAR, a.mes) + '/' + CONVERT(VARCHAR, a.ano), ', ') WITHIN GROUP (ORDER BY a.ano, a.mes) AS periodo_concomitancia, " +
						 "COUNT(*) AS qtde_meses, " +
						 "SUM(a.montanteProventos) AS total_recebido, " +
						 "STRING_AGG(DISTINCT ISNULL(c.nome, 'INDEFINIDO'), ', ') WITHIN GROUP (ORDER BY ISNULL(c.nome, 'INDEFINIDO')) AS cargos_acumulados " +
						 "FROM auditoria.acumulacao a " +
						 "LEFT JOIN auditoria.DetalhamentoAcumulacao da ON da.idAcumulacao = a.id " +
						 "LEFT JOIN Cargo c ON c.id = da.idCargo " +
						 "WHERE a.cpf = ? " +
						 "GROUP BY a.cpf, a.nome";
			Query query = this.entityManager.createNativeQuery(sql);
			query.setParameter(1, cpf);
			return query.getResultList();
		} catch (Exception e) {
			throw new RepositorioException("Erro ao buscar resumo de concomitância.", e.getCause());
		}
	}

	public List<Acumulacao> buscarComCargaHorariaCalculada(Criteria criteria) {
		List<Acumulacao> acumulacoes = entityManager.createQuery(
			"SELECT a FROM Acumulacao a WHERE ...", Acumulacao.class)
			.setHint("org.hibernate.cacheable", true)
			.getResultList();

		List<Object[]> resultados = entityManager.createQuery(
			"SELECT a.id, SUM(c.cargaHorariaMensal) FROM Acumulacao a " +
			"JOIN a.listaDetalhamentoAcumulacao d " +
			"JOIN d.cargo c " +
			"WHERE a IN :acumulacoes " +
			"GROUP BY a.id", Object[].class)
			.setParameter("acumulacoes", acumulacoes)
			.getResultList();

		Map<Long, Integer> cargaHorariaPorId = resultados.stream()
			.collect(Collectors.toMap(
				r -> (Long) r[0],
				r -> ((Number) r[1]).intValue()
			));

		acumulacoes.forEach(a -> {
			a.setCargaHorariaTotal(cargaHorariaPorId.getOrDefault(a.getId(), 0));
			a.getCargaHorariaSemanalTotal();
		});

		return acumulacoes;
	}
}
