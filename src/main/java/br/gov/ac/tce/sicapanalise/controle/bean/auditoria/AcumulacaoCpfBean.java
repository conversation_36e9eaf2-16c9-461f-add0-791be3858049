package br.gov.ac.tce.sicapanalise.controle.bean.auditoria;

import java.io.Serializable;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import javax.faces.context.FacesContext;
import javax.inject.Inject;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.Acumulacao;
import br.gov.ac.tce.sicapanalise.controle.bean.LoginBean;
import br.gov.ac.tce.sicapweb.repositorio.RelatorioConexao;
import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;

import javax.annotation.PostConstruct;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import br.gov.ac.tce.sicapanalise.auditoria.business.AcumulacaoBusiness;
import br.gov.ac.tce.sicapanalise.controle.bean.auditoria.AcumulacaoBean;
import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapanalise.util.Mensagem;
import br.gov.ac.tce.sicapanalise.util.MensagemType;

@Named("acumulacaoCpfBean")
@ViewScoped
public class AcumulacaoCpfBean implements Serializable {

    private static final long serialVersionUID = 1L;


    @Inject
    private AcumulacaoBusiness acumulacaoBusiness;

    @Inject
    private LoginBean loginBean;

    @Inject
    private RelatorioConexao relatorioConexao;

    @Inject
    private AcumulacaoBean acumulacaoBean;

    private String cpf;
    private List<String> competenciasSelecionadas;
    private Collection<Object[]> listaCompetenciasDisponiveis;
    private transient Collection<Object[]> listaResumoConcomitancia;
    private boolean mostrarFiltroCompetencias = false;
    private boolean mostrarDadosAcumulacao = false;
    private Collection<Acumulacao> listaAcumulacoesFiltradas;



    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public Collection<Object[]> getListaResumoConcomitancia() {
        return listaResumoConcomitancia;
    }

    public List<String> getCompetenciasSelecionadas() {
        return competenciasSelecionadas;
    }

    public void setCompetenciasSelecionadas(List<String> competenciasSelecionadas) {
        this.competenciasSelecionadas = competenciasSelecionadas;
    }

    public Collection<Object[]> getListaCompetenciasDisponiveis() {
        return listaCompetenciasDisponiveis;
    }

    public void setListaCompetenciasDisponiveis(Collection<Object[]> listaCompetenciasDisponiveis) {
        this.listaCompetenciasDisponiveis = listaCompetenciasDisponiveis;
    }

    public boolean isMostrarFiltroCompetencias() {
        return mostrarFiltroCompetencias;
    }

    public void setMostrarFiltroCompetencias(boolean mostrarFiltroCompetencias) {
        this.mostrarFiltroCompetencias = mostrarFiltroCompetencias;
    }

    public boolean isMostrarDadosAcumulacao() {
        return mostrarDadosAcumulacao;
    }

    public void setMostrarDadosAcumulacao(boolean mostrarDadosAcumulacao) {
        this.mostrarDadosAcumulacao = mostrarDadosAcumulacao;
    }

    public Collection<Acumulacao> getListaAcumulacoesFiltradas() {
        return listaAcumulacoesFiltradas;
    }

    public void setListaAcumulacoesFiltradas(Collection<Acumulacao> listaAcumulacoesFiltradas) {
        this.listaAcumulacoesFiltradas = listaAcumulacoesFiltradas;
    }

    @PostConstruct
    public void init() {
        this.competenciasSelecionadas = new ArrayList<>();
        this.mostrarFiltroCompetencias = false;
        this.mostrarDadosAcumulacao = false;
    }

    public void pesquisar() {
        System.out.println("[DEBUG] Chamando pesquisar() para CPF: " + this.cpf);
        try {
            String cpfLimpo = this.cpf != null ? this.cpf.replaceAll("[^0-9]", "") : null;
            this.listaResumoConcomitancia = acumulacaoBusiness.resumoConcomitanciaPorCpf(cpfLimpo);
            System.out.println("[DEBUG] Resultado: " + (listaResumoConcomitancia != null ? listaResumoConcomitancia.size() : "null"));

            if (listaResumoConcomitancia != null && !listaResumoConcomitancia.isEmpty()) {
                extrairCompetenciasDisponiveis();
                this.mostrarFiltroCompetencias = true;
            } else {
                this.mostrarFiltroCompetencias = false;
            }

            this.mostrarDadosAcumulacao = false;
            this.competenciasSelecionadas.clear();

        } catch (RepositorioException e) {
            e.printStackTrace();
            Mensagem.setMensagem(MensagemType.ERRO, "Erro ao buscar resumo de concomitância.", "");
            this.mostrarFiltroCompetencias = false;
            this.mostrarDadosAcumulacao = false;
        }
    }

    private void extrairCompetenciasDisponiveis() {
        this.listaCompetenciasDisponiveis = new ArrayList<>();

        if (listaResumoConcomitancia != null) {
            for (Object[] resumo : listaResumoConcomitancia) {

                String periodoConcomitancia = (String) resumo[2];
                if (periodoConcomitancia != null && !periodoConcomitancia.trim().isEmpty()) {
                    String[] competencias = periodoConcomitancia.split(",");
                    for (String competencia : competencias) {
                        String competenciaLimpa = competencia.trim();
                        if (!competenciaLimpa.isEmpty()) {
                            String[] partes = competenciaLimpa.split("/");
                            if (partes.length == 2) {
                                String mes = partes[0];
                                String ano = partes[1];
                                String nomeMes = obterNomeMes(Integer.parseInt(mes));

                                Object[] competenciaObj = {Integer.parseInt(ano), Integer.parseInt(mes), nomeMes};

                                boolean jaExiste = false;
                                for (Object[] existente : listaCompetenciasDisponiveis) {
                                    if (existente[0].equals(competenciaObj[0]) && existente[1].equals(competenciaObj[1])) {
                                        jaExiste = true;
                                        break;
                                    }
                                }

                                if (!jaExiste) {
                                    ((List<Object[]>) listaCompetenciasDisponiveis).add(competenciaObj);
                                }
                            }
                        }
                    }
                }
            }
        }

        if (listaCompetenciasDisponiveis != null && !listaCompetenciasDisponiveis.isEmpty()) {
            ((List<Object[]>) listaCompetenciasDisponiveis).sort((comp1, comp2) -> {
                Integer ano1 = (Integer) comp1[0];
                Integer ano2 = (Integer) comp2[0];
                Integer mes1 = (Integer) comp1[1];
                Integer mes2 = (Integer) comp2[1];

                int compareAno = ano2.compareTo(ano1);
                if (compareAno != 0) {
                    return compareAno;
                }
                return mes2.compareTo(mes1);
            });
        }
    }

    private String obterNomeMes(int mes) {
        String[] nomesMeses = {"", "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho",
                              "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"};
        return mes >= 1 && mes <= 12 ? nomesMeses[mes] : "Inválido";
    }

 
    public void filtrarPorCompetencias() {
        System.out.println("[DEBUG] Filtrando por competências: " + this.competenciasSelecionadas);

        if (competenciasSelecionadas != null && !competenciasSelecionadas.isEmpty()) {
            try {
                String cpfLimpo = this.cpf != null ? this.cpf.replaceAll("[^0-9]", "") : null;

                this.listaAcumulacoesFiltradas = acumulacaoBusiness.listaAcumulacoes(
                    this.competenciasSelecionadas,
                    null, 
                    cpfLimpo,
                    null, 
                    0, 
                    0, 
                    "0", 
                    "0"
                );

                this.mostrarDadosAcumulacao = true;

                this.acumulacaoBean.setListaAcumulacoes(this.listaAcumulacoesFiltradas);
                this.acumulacaoBean.setCurrentLevel(1);

                System.out.println("[DEBUG] Acumulações encontradas: " +
                    (listaAcumulacoesFiltradas != null ? listaAcumulacoesFiltradas.size() : 0));

            } catch (RepositorioException e) {
                e.printStackTrace();
                Mensagem.setMensagem(MensagemType.ERRO, "Erro ao filtrar acumulações por competências.", "");
                this.mostrarDadosAcumulacao = false;
            }
        } else {
            this.mostrarDadosAcumulacao = false;
            Mensagem.setMensagem(MensagemType.ERRO, "Selecione pelo menos uma competência para filtrar.", "");
        }
    }

    public void pesquisarPorCompetenciaEspecifica(String competencia) {
        System.out.println("[DEBUG] Pesquisando por competência específica: " + competencia);

        this.competenciasSelecionadas.clear();

        this.acumulacaoBean.pesquisarPorCpfECompetencia(this.cpf, competencia);

        this.listaAcumulacoesFiltradas = this.acumulacaoBean.getListaAcumulacoes();
        this.mostrarDadosAcumulacao = true;

        System.out.println("[DEBUG] Acumulações encontradas para competência " + competencia + ": " +
            (listaAcumulacoesFiltradas != null ? listaAcumulacoesFiltradas.size() : 0));
    }

    /**
     * Gera relatório consolidado de acumulações para todas as competências selecionadas
     */
    public void gerarRelatorioAcumulacaoCompleto() {
        try {
            if (competenciasSelecionadas == null || competenciasSelecionadas.isEmpty()) {
                Mensagem.setMensagem(MensagemType.ERRO, "Selecione pelo menos uma competência para gerar o relatório.", "");
                return;
            }

            String pathRelatorioDir = FacesContext.getCurrentInstance().getExternalContext().getRealPath("relatorios/") + "/";
            String pathRelatorio = pathRelatorioDir + "acumulacao/" + "relatorioAcumulacao.jasper";

            // Debug: verificar as competências selecionadas
            System.out.println("[DEBUG] CPF: " + this.cpf);
            System.out.println("[DEBUG] Competências selecionadas: " + this.competenciasSelecionadas);

            // Formatar competências para garantir formato MM/YYYY
            List<String> competenciasFormatadas = new ArrayList<>();
            for (String comp : this.competenciasSelecionadas) {
                if (comp != null && comp.contains("/")) {
                    String[] partes = comp.split("/");
                    if (partes.length == 2) {
                        String mes = partes[0].length() == 1 ? "0" + partes[0] : partes[0];
                        competenciasFormatadas.add(mes + "/" + partes[1]);
                    } else {
                        competenciasFormatadas.add(comp);
                    }
                } else {
                    competenciasFormatadas.add(comp);
                }
            }

            String competenciasStr = String.join(", ", competenciasFormatadas);
            System.out.println("[DEBUG] Competências formatadas: " + competenciasStr);

            HashMap<String, Object> parameters = new LinkedHashMap<String, Object>();
            parameters.put("cpf", this.cpf);
            parameters.put("usuario", this.loginBean.getUsuario().getNome());
            parameters.put("competencias", competenciasStr);
            parameters.put("competencia", ""); // Deixa vazio para múltiplas competências
            parameters.put("exercicio", 0); // Não usado para múltiplas competências
            parameters.put("mes", 0); // Não usado para múltiplas competências
            parameters.put("REPORT_IMAGE_DIR", pathRelatorioDir);
            parameters.put("SUBREPORT_DIR", pathRelatorioDir + "acumulacao/");

            JasperPrint jasperPrint;
            try (Connection conexaoJasper = this.relatorioConexao.getConnection()) {
                HttpServletResponse httpServletResponse = (HttpServletResponse) FacesContext.getCurrentInstance()
                        .getExternalContext().getResponse();
                httpServletResponse.addHeader("Content-disposition", "attachment; filename=" + "RelatorioAcumulacaoCompleto-"
                        + this.cpf + "-" + System.currentTimeMillis() + ".pdf");
                ServletOutputStream arquivo = httpServletResponse.getOutputStream();

                jasperPrint = JasperFillManager.fillReport(pathRelatorio, parameters, conexaoJasper);
                JasperExportManager.exportReportToPdfStream(jasperPrint, arquivo);
                arquivo.flush();
                arquivo.close();

                FacesContext.getCurrentInstance().responseComplete();
            }

        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("[ERROR] Erro ao gerar relatório: " + e.getMessage());
            Mensagem.setMensagem(MensagemType.ERRO, "Erro ao gerar relatório de acumulações completo: " + e.getMessage(), "");
        }
    }

    public void testarRelatorio() {
        System.out.println("[DEBUG] Testando relatório...");
        System.out.println("[DEBUG] CPF: " + this.cpf);
        System.out.println("[DEBUG] Competências: " + this.competenciasSelecionadas);

        if (this.competenciasSelecionadas != null) {
            for (int i = 0; i < this.competenciasSelecionadas.size(); i++) {
                System.out.println("[DEBUG] Competência " + i + ": '" + this.competenciasSelecionadas.get(i) + "'");
            }
        }
    }

    public void gerarRelatorioFinanceiroCompleto() {
        try {
            if (competenciasSelecionadas == null || competenciasSelecionadas.isEmpty()) {
                Mensagem.setMensagem(MensagemType.ERRO, "Selecione pelo menos uma competência para gerar o relatório.", "");
                return;
            }

            String pathRelatorioDir = FacesContext.getCurrentInstance().getExternalContext()
                    .getRealPath("relatorios/fichaFinanceira") + "/";
            String pathRelatorio = pathRelatorioDir + "relatorioFinanceiro.jasper";

            HashMap<String, Object> parameters = new LinkedHashMap<String, Object>();
            parameters.put("cpf", this.cpf);
            parameters.put("usuario", this.loginBean.getUsuario().getNome());
            parameters.put("competencias", String.join(", ", this.competenciasSelecionadas));
            parameters.put("REPORT_IMAGE_DIR", pathRelatorioDir);
            parameters.put("SUBREPORT_DIR", pathRelatorioDir);

            JasperPrint jasperPrint;
            try (Connection conexaoJasper = this.relatorioConexao.getConnection()) {
                HttpServletResponse httpServletResponse = (HttpServletResponse) FacesContext.getCurrentInstance()
                        .getExternalContext().getResponse();
                httpServletResponse.addHeader("Content-disposition", "attachment; filename=" + "RelatorioFinanceiroCompleto-"
                        + this.cpf + "-" + System.currentTimeMillis() + ".pdf");
                ServletOutputStream arquivo = httpServletResponse.getOutputStream();

                jasperPrint = JasperFillManager.fillReport(pathRelatorio, parameters, conexaoJasper);
                JasperExportManager.exportReportToPdfStream(jasperPrint, arquivo);
                arquivo.flush();
                arquivo.close();

                FacesContext.getCurrentInstance().responseComplete();
            }

        } catch (Exception e) {
            e.printStackTrace();
            Mensagem.setMensagem(MensagemType.ERRO, "Erro ao gerar relatório financeiro completo.", "");
        }
    }
}
