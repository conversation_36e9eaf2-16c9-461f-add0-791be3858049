package br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao;

import java.io.Serializable;
import java.util.Set;

import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.PostLoad;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.CacheConcurrencyStrategy;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.trilha.HistoricoProcessamentoTrilha;
import br.gov.ac.tce.sicapweb.modelo.SituacaoAcumulacao;

@Entity
@Cacheable
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(schema = "auditoria")
public class Acumulacao implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	private Long id;
	@Column(length = 11, nullable = false)
	private String cpf;
	@Column(length = 500, nullable = false)
	private String nome;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idTrilha", nullable = false)
	private HistoricoProcessamentoTrilha trilha;
	@Column(nullable = false)
	private Integer mes;
	@Column(nullable = false)
	private Integer ano;
	@Column(length = 2)
	private String situacao;
	@Column(precision = 12, scale = 2)
	private Double montanteProventos;
	private Integer quantidadeVinculos;
	private Integer cargaHorariaTotal;
	private Integer quantidadeMunicipiosLotacao;
	private Boolean agentePolitico;
	private Integer quantidadeVinculosNaoPensao;
//	@ManyToOne
//	@JoinColumn(name = "idMetricasMatrizRisco")
//	private MetricasMatrizRisco metricasMatrizRisco;
	@Column(precision = 10, scale = 2)
	private Double pontuacao;
	@OneToMany(mappedBy = "acumulacao",fetch = FetchType.LAZY)
	private Set<DetalhamentoAcumulacao> listaDetalhamentoAcumulacao;
//	@OneToMany(fetch = FetchType.LAZY, mappedBy = "acumulacao" )
//	private Collection<AnaliseAcumulacao> listaAnaliseAcumulacao = new HashSet<>();
	
	@OneToMany(mappedBy = "acumulacao", fetch = FetchType.LAZY)
	private Set<DistribuicaoAcumulacao> listaDistribuicaoAcumulacao;
	
	@OneToMany(mappedBy = "acumulacao", fetch = FetchType.LAZY)
	private Set<AnaliseAcumulacao> listaAnaliseAcumulacao;
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCpf() {
		return cpf;
	}

	public void setCpf(String cpf) {
		this.cpf = cpf;
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}

	public HistoricoProcessamentoTrilha getTrilha() {
		return trilha;
	}

	public void setTrilha(HistoricoProcessamentoTrilha trilha) {
		this.trilha = trilha;
	}

	public Integer getMes() {
		return mes;
	}

	public void setMes(Integer mes) {
		this.mes = mes;
	}

	public Integer getAno() {
		return ano;
	}

	public void setAno(Integer ano) {
		this.ano = ano;
	}

	public Double getMontanteProventos() {
		return montanteProventos;
	}
	

	public String getSituacao() {
		return this.situacao;
	}

	public void setSituacao(String situacao) {
		this.situacao = situacao;
	}

	public void setMontanteProventos(Double montanteProventos) {
		this.montanteProventos = montanteProventos;
	}

	public Integer getQuantidadeVinculos() {
		return quantidadeVinculos;
	}

	public void setQuantidadeVinculos(Integer quantidadeVinculos) {
		this.quantidadeVinculos = quantidadeVinculos;
	}

	public Integer getCargaHorariaTotal() {
		return cargaHorariaTotal;
	}

	public void setCargaHorariaTotal(Integer cargaHorariaTotal) {
		this.cargaHorariaTotal = cargaHorariaTotal;
	}

	public Integer getQuantidadeMunicipiosLotacao() {
		return quantidadeMunicipiosLotacao;
	}

	public void setQuantidadeMunicipiosLotacao(Integer quantidadeMunicipiosLotacao) {
		this.quantidadeMunicipiosLotacao = quantidadeMunicipiosLotacao;
	}

	public Boolean getAgentePolitico() {
		return agentePolitico;
	}

	public void setAgentePolitico(Boolean agentePolitico) {
		this.agentePolitico = agentePolitico;
	}

	public Integer getQuantidadeVinculosNaoPensao() {
		return quantidadeVinculosNaoPensao;
	}

	public void setQuantidadeVinculosNaoPensao(Integer quantidadeVinculosNaoPensao) {
		this.quantidadeVinculosNaoPensao = quantidadeVinculosNaoPensao;
	}


	public Double getPontuacao() {
		return pontuacao;
	}

	public void setPontuacao(Double pontuacao) {
		this.pontuacao = pontuacao;
	}
	

	public Set<DetalhamentoAcumulacao> getListaDetalhamentoAcumulacao() {
		return listaDetalhamentoAcumulacao;
	}

	public void setListaDetalhamentoAcumulacao(Set<DetalhamentoAcumulacao> listaDetalhamentoAcumulacao) {
		this.listaDetalhamentoAcumulacao = listaDetalhamentoAcumulacao;
	}
	
	public Set<DistribuicaoAcumulacao> getListaDistribuicaoAcumulacao() {
		return listaDistribuicaoAcumulacao;
	}

	public void setListaDistribuicaoAcumulacao(Set<DistribuicaoAcumulacao> listaDistribuicaoAcumulacao) {
		this.listaDistribuicaoAcumulacao = listaDistribuicaoAcumulacao;
	}
	
	public Set<AnaliseAcumulacao> getListaAnaliseAcumulacao() {
		return listaAnaliseAcumulacao;
	}

	public void setListaAnaliseAcumulacao(Set<AnaliseAcumulacao> listaAnaliseAcumulacao) {
		this.listaAnaliseAcumulacao = listaAnaliseAcumulacao;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Acumulacao other = (Acumulacao) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	public String getSituacaoTexto() {
		if(this.situacao != null)
			return SituacaoAcumulacao.parse(this.situacao).getDescricao();
		return "";
	}

	public Integer getCargaHorariaSemanalTotal() {
    if (this.cargaHorariaTotal != null) {
        return this.cargaHorariaTotal / 4;
    }
    return 0;
	}

	private Integer calcularCargaHorariaSemanal() {
		if (this.cargaHorariaTotal != null) {
			return this.cargaHorariaTotal / 4;
		}
		
		if (this.listaDetalhamentoAcumulacao == null || this.listaDetalhamentoAcumulacao.isEmpty()) {
			return 0;
		}
		
		return this.listaDetalhamentoAcumulacao.stream()
			.mapToInt(d -> d.getCargaHorariaSemanal() != null ? d.getCargaHorariaSemanal() : 0)
			.sum();
	}
	/* 
	@PostLoad
	private void calcularTotais() {
		if (this.listaDetalhamentoAcumulacao != null) {
			this.cargaHorariaTotal = this.listaDetalhamentoAcumulacao.stream()
				.mapToInt(d -> d.getCargo() != null && d.getCargo().getCargaHorariaMensal() != null 
					? d.getCargo().getCargaHorariaMensal() 
					: 0)
				.sum();
		}
	}*/

//	public boolean getTemProcesso() {
//		Optional<AnaliseAcumulacao> analiseAcumulacao = this.listaAnaliseAcumulacao.stream()
//				.filter(aa -> aa.getAnalise().getNumeroProcessoEletronico() != null && !aa.getAnalise().getNumeroProcessoEletronico().isEmpty())
//				.findAny();
//		if (analiseAcumulacao.isPresent()) {
//			return true;
//		}
//		return false;
//	}
//	
//	public boolean getTemDocumentos() {
//		for (AnaliseAcumulacao analiseAcumulacao : listaAnaliseAcumulacao) {
//			for (SolicitacaoDocumento solicitacaoDocumento : analiseAcumulacao.getAnalise().getListaSolicitacaoDocumento()) {
//				if (solicitacaoDocumento.getSituacao().equals(SituacaoSolicitacaoDocumento.RE)) {
//					return true;
//				}
//			}
//			
//			Optional<RelatorioAnalise> relatorioAnalise = analiseAcumulacao.getAnalise().getListaRelatorioAnalise().stream().findAny();
//			
//			if (relatorioAnalise.isPresent()) {
//				return true;
//			}
//		}
//		
//		return false;
//	}
	
//	public boolean getDistribuida() {
//		Optional<DistribuicaoAcumulacao> distribuicaoAcumulacao = this.listaDistribuicaoAcumulacao.stream().filter(da -> da.getAtivo()).findAny();
//		if(distribuicaoAcumulacao.isPresent())
//			return true; 
//		return false;
//	}

	
}
