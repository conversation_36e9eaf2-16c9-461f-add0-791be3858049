package br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.trilha.HistoricoProcessamentoTrilha;
import br.gov.ac.tce.sicap.util.Mes;
import br.gov.ac.tce.sicapweb.modelo.cargo.Cargo;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.pessoa.Beneficiario;
import br.gov.ac.tce.sicapweb.modelo.unidadelotacao.Municipio;

@Entity
@Table(schema = "auditoria")
public class DetalhamentoAcumulacao implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	private Long id;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idTrilha")
	private HistoricoProcessamentoTrilha trilha;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idAcumulacao", nullable = false)
	private Acumulacao acumulacao;
	@Column(length = 11, nullable = false)
	private String cpf;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idBeneficiario", nullable = false)
	private Beneficiario beneficiario;
	@Column(nullable = false)
	private Integer mes;
	@Column(nullable = false)
	private Integer ano;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idEntidadeCjur", nullable = false)
	private Entidade entidade;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idCargo", nullable = false)
	private Cargo cargo;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idMunicipio", nullable = false)
	private Municipio municipio;
	@Column(length = 32, nullable = false)
	private String hash;
	@Transient
	private Integer chTotal = 0;
	@Transient
	private BigDecimal totalLiquido;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public HistoricoProcessamentoTrilha getTrilha() {
		return trilha;
	}

	public void setTrilha(HistoricoProcessamentoTrilha trilha) {
		this.trilha = trilha;
	}

	public Acumulacao getAcumulacao() {
		return acumulacao;
	}

	public void setAcumulacao(Acumulacao acumulacao) {
		this.acumulacao = acumulacao;
	}

	public String getCpf() {
		return cpf;
	}

	public void setCpf(String cpf) {
		this.cpf = cpf;
	}

	public Beneficiario getBeneficiario() {
		return beneficiario;
	}

	public void setBeneficiario(Beneficiario beneficiario) {
		this.beneficiario = beneficiario;
	}

	public Mes getMes() {
		return Mes.parse(this.mes);
	}

	public void setMes(Mes mes) {
		this.mes = mes.getNumero();
	}

	public Integer getAno() {
		return ano;
	}

	public void setAno(Integer ano) {
		this.ano = ano;
	}

	public Entidade getEntidade() {
		return entidade;
	}

	public void setEntidade(Entidade entidade) {
		this.entidade = entidade;
	}

	public Cargo getCargo() {
		return cargo;
	}

	public void setCargo(Cargo cargo) {
		this.cargo = cargo;
	}

	public Municipio getMunicipio() {
		return municipio;
	}

	public void setMunicipio(Municipio municipio) {
		this.municipio = municipio;
	}

	public String getHash() {
		return hash;
	}

	public void setHash(String hash) {
		this.hash = hash;
	}

	
	public String getCargoNome() {
		String cargoNome = "";
		if (this.cargo == null) {
			cargoNome = "PENSIONISTA";
		} else {
			cargoNome = this.cargo.getNome();
		}
		return cargoNome;
	}

	public Integer getCargaHorariaSemanal() {
		if (this.cargo != null && this.cargo.getCargaHorariaMensal() != null) {
			return this.cargo.getCargaHorariaMensal() / 4;
		}
		return 0;
	}

	public BigDecimal getTotalLiquido() {
		return totalLiquido;
	}

	public void setTotalLiquido(BigDecimal totalLiquido) {
		this.totalLiquido = totalLiquido;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((acumulacao == null) ? 0 : acumulacao.hashCode());
		result = prime * result + ((ano == null) ? 0 : ano.hashCode());
		result = prime * result + ((beneficiario == null) ? 0 : beneficiario.hashCode());
		result = prime * result + ((cargo == null) ? 0 : cargo.hashCode());
		result = prime * result + ((cpf == null) ? 0 : cpf.hashCode());
		result = prime * result + ((entidade == null) ? 0 : entidade.hashCode());
		result = prime * result + ((hash == null) ? 0 : hash.hashCode());
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		result = prime * result + ((mes == null) ? 0 : mes.hashCode());
		result = prime * result + ((municipio == null) ? 0 : municipio.hashCode());
		result = prime * result + ((trilha == null) ? 0 : trilha.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DetalhamentoAcumulacao other = (DetalhamentoAcumulacao) obj;
		if (acumulacao == null) {
			if (other.acumulacao != null)
				return false;
		} else if (!acumulacao.equals(other.acumulacao))
			return false;
		if (ano == null) {
			if (other.ano != null)
				return false;
		} else if (!ano.equals(other.ano))
			return false;
		if (beneficiario == null) {
			if (other.beneficiario != null)
				return false;
		} else if (!beneficiario.equals(other.beneficiario))
			return false;
		if (cargo == null) {
			if (other.cargo != null)
				return false;
		} else if (!cargo.equals(other.cargo))
			return false;
		if (cpf == null) {
			if (other.cpf != null)
				return false;
		} else if (!cpf.equals(other.cpf))
			return false;
		if (entidade == null) {
			if (other.entidade != null)
				return false;
		} else if (!entidade.equals(other.entidade))
			return false;
		if (hash == null) {
			if (other.hash != null)
				return false;
		} else if (!hash.equals(other.hash))
			return false;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		if (mes == null) {
			if (other.mes != null)
				return false;
		} else if (!mes.equals(other.mes))
			return false;
		if (municipio == null) {
			if (other.municipio != null)
				return false;
		} else if (!municipio.equals(other.municipio))
			return false;
		if (trilha == null) {
			if (other.trilha != null)
				return false;
		} else if (!trilha.equals(other.trilha))
			return false;
		return true;
	}
	
}
