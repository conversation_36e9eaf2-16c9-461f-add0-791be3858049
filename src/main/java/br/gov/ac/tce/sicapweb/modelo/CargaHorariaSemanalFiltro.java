package br.gov.ac.tce.sicapweb.modelo;

public enum CargaHorariaSemanalFiltro {
	TODAS("0", "Todas"),
	MAIOR_QUE_60("1", "CH Semanal > 60"),
	MENOR_IGUAL_60("2", "CH Semanal ≤ 60");

	private String id;
	private String descricao;

	private CargaHorariaSemanalFiltro(String id, String descricao) {
		this.id = id;
		this.descricao = descricao;
	}

	public String getId() {
		return id;
	}

	public String getDescricao() {
		return descricao;
	}

	public static CargaHorariaSemanalFiltro parse(String id) {
		CargaHorariaSemanalFiltro filtro = null;
		for (CargaHorariaSemanalFiltro item : CargaHorariaSemanalFiltro.values()) {
			if (item.getId().equals(id)) {
				filtro = item;
				break;
			}
		}
		return filtro;
	}
}
