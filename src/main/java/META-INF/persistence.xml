<?xml version="1.0" encoding="UTF-8"?>
<persistence version="2.2"
	xmlns="http://xmlns.jcp.org/xml/ns/persistence"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/persistence http://xmlns.jcp.org/xml/ns/persistence/persistence_2_2.xsd">
	<persistence-unit name="sicapAnalise"
		transaction-type="JTA">

		<provider>org.hibernate.ejb.HibernatePersistence</provider>
		<jta-data-source>java:jboss/datasources/sicapAnaliseDS</jta-data-source>

		<properties>
			<property name="hibernate.dialect"
				value="org.hibernate.dialect.SQLServer2012Dialect" />
			<property name="hibernate.connection.characterEncoding"
				value="ISO-8859-1" />
			<property name="hibernate.connection.useUnicode"
				value="true" />
			<property name="hibernate.connection.charSet"
				value="ISO-8859-1" />
			<property name="hibernate.enable_lazy_load_no_trans"
				value="true" />

			<!-- Configurações de performance para evitar OutOfMemoryError -->
			<property name="hibernate.jdbc.batch_size" value="25" />
			<property name="hibernate.jdbc.fetch_size" value="100" />
			<property name="hibernate.order_inserts" value="true" />
			<property name="hibernate.order_updates" value="true" />
			<property name="hibernate.jdbc.batch_versioned_data" value="true" />
			<property name="hibernate.connection.provider_disables_autocommit" value="true" />

			<property name="hibernate.cache.use_second_level_cache" value="true"/>
			<property name="hibernate.cache.use_query_cache" value="true"/>
			<property name="hibernate.generate_statistics" value="false"/>
		</properties>
	</persistence-unit>

	<persistence-unit name="cjur" transaction-type="JTA">

		<provider>org.hibernate.ejb.HibernatePersistence</provider>
		<jta-data-source>java:jboss/datasources/pessoalDS</jta-data-source>

		<properties>
			<property name="hibernate.dialect"
				value="org.hibernate.dialect.SQLServer2012Dialect" />
			<property name="hibernate.connection.useUnicode"
				value="true" />
			<property name="hibernate.connection.characterEncoding"
				value="ISO-8859-1" />
			<property name="hibernate.connection.charSet"
				value="ISO-8859-1" />
		</properties>
	</persistence-unit>
	
	<persistence-unit name="tce" transaction-type="JTA">
	
		<provider>org.hibernate.ejb.HibernatePersistence</provider>
		<jta-data-source>java:jboss/datasources/tceDS</jta-data-source>
	
		<properties>
			<property name="hibernate.dialect"
				value="org.hibernate.dialect.SQLServer2012Dialect" />
			<property name="hibernate.connection.useUnicode"
				value="true" />
			<property name="hibernate.connection.characterEncoding"
				value="ISO-8859-1" />
			<property name="hibernate.connection.charSet"
				value="ISO-8859-1" />
		</properties>
	</persistence-unit>
</persistence>
