<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.3.1.final using JasperReports Library version 6.3.1  -->
<!-- 2018-03-23T10:34:04 -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="subRelatorioAcumulacaoVinculosMultiplas" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="800" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="4de3fdd0-fb92-43a5-af2e-3ff2c9c0ea70">
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="346"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="647"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="730"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="256"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="sicap"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="cpf" class="java.lang.String"/>
	<parameter name="competencias" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT DISTINCT da.idBeneficiario,
	       e.idEntidadeCjur,
	       e.nome AS entidade,
	       b.matricula,
	       c.nome AS cargo,
	       c.cargaHorariaMensal,
	       (c.cargaHorariaMensal / 4) as cargaHorariaSemanal,
	       CONVERT (VARCHAR (10), vf.dataAdmissao, 103) AS dataAdmissao,
	       CASE vf.regimePrevidenciario WHEN 1 THEN 'RGPS' WHEN 2 THEN 'RPPS' END AS regimePrevidenciario,
	       CASE vf.tipoServidor WHEN 1 THEN 'Civil' WHEN 2 THEN 'Militar' END AS tipoServidor,
	       tv.descricao as tipoVinculo,
	       CONVERT (VARCHAR (10), hf.dataOcorrencia, 103) AS dataOcorrencia,
	       sf.descricao AS situacaoFuncional,
	       lotacao.nome AS unidade,
	       (m.nome + ' - ' + u.nome) AS cidade,
	       STRING_AGG(CONVERT(VARCHAR, da.mes) + '/' + CONVERT(VARCHAR, da.ano), ', ') WITHIN GROUP (ORDER BY da.ano, da.mes) AS competenciasEncontradas
FROM auditoria.DetalhamentoAcumulacao da
     LEFT JOIN Entidade e ON e.idEntidadeCjur = da.idEntidadeCjur
     LEFT JOIN Beneficiario b ON b.id = da.idBeneficiario
     LEFT JOIN Cargo c ON c.id = da.idCargo
     LEFT JOIN VinculoFuncional vf ON vf.idBeneficiario = da.idBeneficiario AND
      vf.idCargo = da.idCargo AND vf.registroAtivo = 1
     LEFT JOIN TipoVinculo tv ON tv.id = vf.idTipoVinculo
     LEFT JOIN Municipio m ON m.id = da.idMunicipio
     LEFT JOIN Uf u ON u.id = m.idUf
     LEFT JOIN 
     (SELECT MAX(hf.id) as id, hf.idBeneficiario FROM HistoricoFuncional hf
      GROUP BY hf.idBeneficiario) historico ON historico.idBeneficiario =
       da.idBeneficiario
     LEFT JOIN HistoricoFuncional hf ON hf.id = historico.id
     LEFT JOIN SituacaoFuncional sf ON sf.id = hf.idSituacaoFuncional
     LEFT JOIN 
     (SELECT cc.idBeneficiario, cc.ano, cc.mes, ul.nome FROM ContraCheque cc
      INNER JOIN UnidadeLotacao ul ON ul.id = cc.idUnidadeLotacao) lotacao ON
       lotacao.idBeneficiario = da.idBeneficiario AND lotacao.ano = da.ano AND
        lotacao.mes = da.mes
WHERE da.cpf = $P{cpf}
GROUP BY da.idBeneficiario,
         e.idEntidadeCjur,
         e.nome,
         b.matricula,
         c.nome,
         c.cargaHorariaMensal,
         (c.cargaHorariaMensal / 4),
         CONVERT (VARCHAR (10), vf.dataAdmissao, 103),
         CASE vf.regimePrevidenciario
           WHEN 1 THEN 'RGPS'
           WHEN 2 THEN 'RPPS'
         END,
         CASE vf.tipoServidor
           WHEN 1 THEN 'Civil'
           WHEN 2 THEN 'Militar'
         END,
         tv.descricao,
         CONVERT (VARCHAR (10), hf.dataOcorrencia, 103),
         sf.descricao,
         lotacao.nome,
         (m.nome + ' - ' + u.nome)]]>
	</queryString>
	<field name="idBeneficiario" class="java.lang.Long"/>
	<field name="idEntidadeCjur" class="java.lang.Integer"/>
	<field name="entidade" class="java.lang.String"/>
	<field name="matricula" class="java.lang.Integer"/>
	<field name="cargo" class="java.lang.String"/>
	<field name="cargaHorariaMensal" class="java.lang.Integer"/>
	<field name="cargaHorariaSemanal" class="java.lang.Integer"/>
	<field name="dataAdmissao" class="java.lang.String"/>
	<field name="regimePrevidenciario" class="java.lang.String"/>
	<field name="tipoServidor" class="java.lang.String"/>
	<field name="tipoVinculo" class="java.lang.String"/>
	<field name="dataOcorrencia" class="java.lang.String"/>
	<field name="situacaoFuncional" class="java.lang.String"/>
	<field name="unidade" class="java.lang.String"/>
	<field name="cidade" class="java.lang.String"/>
	<field name="competenciasEncontradas" class="java.lang.String"/>
	<columnHeader>
		<band height="25" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<rectangle>
				<reportElement mode="Opaque" x="0" y="0" width="802" height="25" backcolor="#E6E6E6" uuid="a1b2c3d4-e5f6-7890-abcd-ef1234567890"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="2" y="2" width="150" height="21" uuid="b2c3d4e5-f6a7-8901-bcde-f23456789012"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Entidade]]></text>
			</staticText>
			<staticText>
				<reportElement x="154" y="2" width="60" height="21" uuid="c3d4e5f6-a7b8-9012-cdef-************"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Matrícula]]></text>
			</staticText>
			<staticText>
				<reportElement x="216" y="2" width="120" height="21" uuid="d4e5f6a7-b8c9-0123-def0-************"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Cargo]]></text>
			</staticText>
			<staticText>
				<reportElement x="338" y="2" width="40" height="21" uuid="e5f6a7b8-c9d0-1234-ef01-************"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[CH Mensal]]></text>
			</staticText>
			<staticText>
				<reportElement x="380" y="2" width="40" height="21" uuid="f6a7b8c9-d0e1-2345-f012-************"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[CH Semanal]]></text>
			</staticText>
			<staticText>
				<reportElement x="422" y="2" width="60" height="21" uuid="a7b8c9d0-e1f2-3456-0123-************"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Data Admissão]]></text>
			</staticText>
			<staticText>
				<reportElement x="484" y="2" width="40" height="21" uuid="b8c9d0e1-f2a3-4567-1234-************"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Regime]]></text>
			</staticText>
			<staticText>
				<reportElement x="526" y="2" width="40" height="21" uuid="c9d0e1f2-a3b4-5678-2345-************"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Tipo]]></text>
			</staticText>
			<staticText>
				<reportElement x="568" y="2" width="60" height="21" uuid="d0e1f2a3-b4c5-6789-3456-012345678901"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Vínculo]]></text>
			</staticText>
			<staticText>
				<reportElement x="630" y="2" width="80" height="21" uuid="e1f2a3b4-c5d6-7890-4567-123456789012"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Situação]]></text>
			</staticText>
			<staticText>
				<reportElement x="712" y="2" width="88" height="21" uuid="f2a3b4c5-d6e7-8901-5678-************"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Competências]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<rectangle>
				<reportElement x="0" y="0" width="802" height="20" uuid="a3b4c5d6-e7f8-9012-6789-************"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement x="2" y="2" width="150" height="16" uuid="b4c5d6e7-f8a9-0123-7890-************"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{entidade}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="154" y="2" width="60" height="16" uuid="c5d6e7f8-a9b0-1234-8901-************"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{matricula}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="216" y="2" width="120" height="16" uuid="d6e7f8a9-b0c1-2345-9012-************"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cargo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="338" y="2" width="40" height="16" uuid="e7f8a9b0-c1d2-3456-0123-************"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cargaHorariaMensal}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="380" y="2" width="40" height="16" uuid="f8a9b0c1-d2e3-4567-1234-************"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cargaHorariaSemanal}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="422" y="2" width="60" height="16" uuid="a9b0c1d2-e3f4-5678-2345-************"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataAdmissao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="484" y="2" width="40" height="16" uuid="b0c1d2e3-f4a5-6789-3456-012345678901"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{regimePrevidenciario}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="526" y="2" width="40" height="16" uuid="c1d2e3f4-a5b6-7890-4567-123456789012"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipoServidor}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="568" y="2" width="60" height="16" uuid="d2e3f4a5-b6c7-8901-5678-************"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipoVinculo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="630" y="2" width="80" height="16" uuid="e3f4a5b6-c7d8-9012-6789-************"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{situacaoFuncional}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="712" y="2" width="88" height="16" uuid="f4a5b6c7-d8e9-0123-7890-************"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{competenciasEncontradas}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
