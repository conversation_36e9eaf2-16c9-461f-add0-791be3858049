<ui:composition
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:h="http://xmlns.jcp.org/jsf/html"
  xmlns:f="http://xmlns.jcp.org/jsf/core"
  xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
  xmlns:p="http://primefaces.org/ui"
  xmlns:pe="http://primefaces.org/ui/extensions"
  xmlns:fn="http://xmlns.jcp.org/jsp/jstl/functions"
  xmlns:c="http://java.sun.com/jsp/jstl/core"
  template="/resources/template/template.xhtml"
>
  <ui:define name="content">
    <style type="text/css">
      .ui-row-editor .ui-icon-pencil {
        margin-left: 12px;
      }
      span.ui-row-editor {
        display: inline-block !important;
      }
    </style>
    <h:form id="frmAcumulacoesPorCpf" prependId="false">
      <p:fieldset legend="Consulta de Concomitância por CPF">
        <div class="ui-g">
          <div class="ui-g-12 ui-md-6 ui-lg-4">
            <h:panelGroup>
              <h:outputText value="CPF: " styleClass="FontBold" />
              <p:inputText value="#{acumulacaoCpfBean.cpf}" />
            </h:panelGroup>
          </div>
          <div class="ui-g-12 ui-md-6 ui-lg-4">
            <p:commandButton
              value="Pesquisar"
              style="margin-top: 20px; width: auto"
              icon="fa fa-fw fa-search white"
              actionListener="#{acumulacaoCpfBean.pesquisar()}"
              update="@form"
            />
          </div>
        </div>
      </p:fieldset>
      <div class="EmptyBox10" />
      <p:fieldset legend="Resumo de Concomitância">
        <p:dataTable
          id="tblResumoConcomitancia"
          var="resumo"
          value="#{acumulacaoCpfBean.listaResumoConcomitancia}"
          emptyMessage="Nenhum resultado encontrado."
          rows="10"
          paginator="true"
          paginatorAlwaysVisible="false"
          paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
          rowsPerPageTemplate="10,20,30,40,50"
          currentPageReportTemplate="página {currentPage} de {totalPages}"
          paginatorPosition="bottom"
        >
          <p:column headerText="CPF">
            <p:commandLink
              actionListener="#{acumulacaoBean.pesquisarPorCpfENavegar(resumo[0])}"
              update="@form"
            >
              <h:outputText value="#{resumo[0]}">
                <f:converter converterId="converter.CpfConverter" />
              </h:outputText>
              <f:setPropertyActionListener
                value="#{resumo[0]}"
                target="#{detalhamentoAcumulacaoBean.idAcumulacao}"
              />
            </p:commandLink>
          </p:column>
          <p:column headerText="Nome">
            <p:commandLink
              actionListener="#{acumulacaoBean.pesquisarPorCpfENavegar(resumo[0])}"
              update="@form"
            >
              <h:outputText value="#{resumo[1]}" />
            </p:commandLink>
          </p:column>
          <p:column headerText="Período de Concomitância">
            <ui:repeat
              value="#{acumulacaoBean.dividirPeriodoConcomitancia(resumo[2])}"
              var="competencia"
              varStatus="status"
            >
              <p:commandLink
                actionListener="#{acumulacaoCpfBean.pesquisarPorCompetenciaEspecifica(competencia)}"
                update="@form"
                style="text-decoration: underline; color: #007bff"
              >
                <h:outputText value="#{competencia}" />
              </p:commandLink>
              <h:outputText value="#{status.last ? '' : ', '}" />
            </ui:repeat>
          </p:column>
          <p:column headerText="Qtde de Meses Pagos Simultaneamente">
            <h:outputText value="#{resumo[3]}" />
          </p:column>
          <p:column headerText="Total Recebido Concomitantemente">
            <h:outputText value="#{resumo[4]}">
              <f:convertNumber pattern="R$ ###,##0.00" locale="pt_br" />
            </h:outputText>
          </p:column>
          <p:column headerText="Cargos Acumulados">
            <h:outputText value="#{resumo[5]}" />
          </p:column>
        </p:dataTable>
      </p:fieldset>

      <p:fieldset
        legend="Selecionar Múltiplas Competências"
        rendered="#{acumulacaoCpfBean.mostrarFiltroCompetencias}"
      >
        <div class="ui-g">
          <div class="ui-g-12 ui-md-8 ui-lg-6">
            <h:panelGroup>
              <h:outputText
                value="Competências Disponíveis: "
                styleClass="FontBold"
              />
              <p:selectCheckboxMenu
                value="#{acumulacaoCpfBean.competenciasSelecionadas}"
                label="Selecione as competências"
                filter="true"
                filterMatchMode="contains"
                panelStyle="width:350px"
                scrollHeight="200"
              >
                <f:selectItems
                  value="#{acumulacaoCpfBean.listaCompetenciasDisponiveis}"
                  var="competencia"
                  itemLabel="#{competencia[2]}/#{competencia[0]}"
                  itemValue="#{competencia[1]}/#{competencia[0]}"
                />
              </p:selectCheckboxMenu>
            </h:panelGroup>
          </div>
          <div class="ui-g-12 ui-md-4 ui-lg-6">
            <p:commandButton
              value="Filtrar"
              style="margin-top: 20px; width: auto"
              icon="fa fa-fw fa-filter white"
              actionListener="#{acumulacaoCpfBean.filtrarPorCompetencias()}"
              update="@form"
            />
          </div>
        </div>
      </p:fieldset>

      <div class="EmptyBox10" />

      <p:fieldset
        legend="Acumulações do Servidor"
        rendered="#{(not empty acumulacaoBean.listaAcumulacoes) or (acumulacaoCpfBean.mostrarDadosAcumulacao and not empty acumulacaoCpfBean.listaAcumulacoesFiltradas)}"
      >
        <pe:masterDetail
          id="masterDetail"
          level="#{acumulacaoBean.currentLevel}"
          showAllBreadcrumbItems="true"
        >
          <f:facet name="header">
            <p:messages showDetail="false" showSummary="true" />
          </f:facet>
          <pe:masterDetailLevel id="detailInicio" level="1">
            <f:facet name="label">
              <h:outputText value="Início" />
            </f:facet>

            <!-- Botões de Relatórios Completos - Aparecem apenas quando há dados filtrados -->
            <div
              class="ui-g"
              style="margin-bottom: 20px"
              rendered="#{acumulacaoCpfBean.mostrarDadosAcumulacao}"
            >
              <div class="ui-g-12 ui-md-6">
                <p:commandButton
                  value="Relatório de Acumulações Completo"
                  icon="fa fa-fw fa-file-pdf-o white"
                  actionListener="#{acumulacaoCpfBean.gerarRelatorioAcumulacaoCompleto()}"
                  ajax="false"
                  style="width: auto; margin-right: 10px"
                  disabled="true"
                  title="Funcionalidade em desenvolvimento"
                />
              </div>
              <div class="ui-g-12 ui-md-6">
                <p:commandButton
                  value="Relatório Financeiro de Acumulações e Verbas Completo"
                  icon="fa fa-fw fa-file-pdf-o white"
                  actionListener="#{acumulacaoCpfBean.gerarRelatorioFinanceiroCompleto()}"
                  ajax="false"
                  style="width: auto"
                  disabled="true"
                  title="Funcionalidade em desenvolvimento"
                />
              </div>
            </div>

            <!--   <c:if test="#{loginBean.getPermissao('SICAP_ADMINISTRADOR')}">
              <p:commandButton
                id="btnDistribuirAcumulacoes"
                style="width: auto"
                icon="fa fa-fw fa-copy white"
                rendered="#{loginBean.perfilDistribuicao}"
                value="Iniciar Distribuição"
                actionListener="#{acumulacaoBean.temAcumulacaoSelecionada()}"
                update="@form dlgIniciarDistribuicao"
              >
                <f:setPropertyActionListener
                  value="#{acumulacaoBean.acumulacoesSelecionadas}"
                  target="#{distribuicaoAcumulacaoBean.listaAcumulacao}"
                />
              </p:commandButton>
              <div class="EmptyBox10" />
            </c:if> -->
            <p:dataTable
              id="tblAcm"
              var="acumulacao"
              widgetVar="tblAcmVar"
              rowKey="#{acumulacao.id}"
              tableStyle="table-layout: auto;"
              reflow="true"
              selection="#{acumulacaoBean.acumulacoesSelecionadas}"
              disabledSelection="#{acumulacao.situacao != 'AG' and acumulacao.situacao != null}"
              value="#{acumulacaoCpfBean.mostrarDadosAcumulacao ? acumulacaoCpfBean.listaAcumulacoesFiltradas : acumulacaoBean.listaAcumulacoes}"
              sortBy="#{acumulacao.pontuacao}"
              sortOrder="descending"
              emptyMessage="Nenhuma acumulação encontrada."
              rows="15"
              paginatorAlwaysVisible="false"
              paginator="true"
              paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
              rowsPerPageTemplate="15,20,30,40,50,100,300,500"
              currentPageReportTemplate="página {currentPage} de {totalPages}"
              paginatorPosition="bottom"
            >
              <p:column headerText="Cód. Acumulação" styleClass="TexAlCenter">
                <h:outputText value="#{acumulacao.id}" />
              </p:column>
              <p:column
                headerText="Competência"
                width="7%"
                styleClass="TexAlCenter"
              >
                <h:outputText value="#{acumulacao.mes}/#{acumulacao.ano}" />
              </p:column>
              <p:column headerText="CPF" width="10%" styleClass="TexAlCenter">
                <h:outputText value="#{acumulacao.cpf}">
                  <f:converter converterId="converter.CpfConverter" />
                </h:outputText>
              </p:column>
              <p:column
                headerText="Nome"
                width="25%"
                sortBy="#{acumulacao.nome}"
              >
                <p:commandLink value="#{acumulacao.nome}" update="@form">
                  <f:setPropertyActionListener
                    value="#{acumulacao}"
                    target="#{acumulacaoBean.acumulacao}"
                  />
                  <f:setPropertyActionListener
                    value="#{acumulacao.id}"
                    target="#{detalhamentoAcumulacaoBean.idAcumulacao}"
                  />
                  <pe:selectDetailLevel contextValue="#{acumulacao}" />
                </p:commandLink>
              </p:column>
              <p:column headerText="Montante Proventos" width="8%">
                <h:outputText
                  value="#{acumulacao.montanteProventos}"
                  style="float: right"
                >
                  <f:convertNumber pattern="R$ ###,##0.00" locale="pt_br" />
                </h:outputText>
              </p:column>
              <p:column
                headerText="Situação"
                width="10%"
                styleClass="TexAlCenter"
                sortBy="#{acumulacao.situacao}"
              >
                <h:outputText value="#{acumulacao.situacaoTexto}" />
              </p:column>
              <p:column
                headerText="Quantidade Vínculos"
                styleClass="TexAlCenter"
                sortBy="#{acumulacao.quantidadeVinculos}"
              >
                <h:outputText value="#{acumulacao.quantidadeVinculos}" />
              </p:column>
              <p:column
                headerText="Carga Horária Semanal Total"
                styleClass="TexAlCenter"
              >
                <h:outputText value="#{acumulacao.cargaHorariaSemanalTotal}" />
              </p:column>
              <p:column
                headerText="Quantidade Municípios Lotação"
                styleClass="TexAlCenter"
              >
                <h:outputText
                  value="#{acumulacao.quantidadeMunicipiosLotacao}"
                />
              </p:column>
              <p:column headerText="Agente Político?" styleClass="TexAlCenter">
                <h:outputText
                  value="#{acumulacao.agentePolitico ? 'Sim' : 'Não'}"
                />
              </p:column>
              <p:column
                headerText="Quantidade Vínculos Não Pensão"
                styleClass="TexAlCenter"
              >
                <h:outputText
                  value="#{acumulacao.quantidadeVinculosNaoPensao}"
                />
              </p:column>
              <p:column headerText="Pontuação" sortBy="#{acumulacao.pontuacao}">
                <h:outputText
                  value="#{acumulacao.pontuacao}"
                  style="float: right"
                >
                  <f:convertNumber pattern="#0.00" locale="pt_br" />
                </h:outputText>
              </p:column>
              <f:facet name="footer">
                <h:outputLabel
                  value="Total de acumulações: #{acumulacaoCpfBean.mostrarDadosAcumulacao ? acumulacaoCpfBean.listaAcumulacoesFiltradas.size() : acumulacaoBean.listaAcumulacoes.size()}"
                />
              </f:facet>
            </p:dataTable>
          </pe:masterDetailLevel>
          <pe:masterDetailLevel
            id="detailDadosAcumulacao"
            level="2"
            contextVar="acumulacao"
          >
            <f:facet name="label">
              <h:outputText value="Dados da Acumulação" />
            </f:facet>
            <p:accordionPanel multiple="true" activeIndex="0,1,2">
              <p:tab title="Servidor">
                <h:panelGrid columns="2">
                  <h:outputText
                    value="Competência: "
                    styleClass="Fs12 FontBold"
                  />
                  <h:outputText
                    value="#{acumulacao.mes}/#{acumulacao.ano}"
                    styleClass="Fs12"
                  />
                  <h:outputText value="CPF: " styleClass="Fs12 FontBold" />
                  <h:outputText value="#{acumulacao.cpf}" styleClass="Fs12">
                    <f:converter converterId="converter.CpfConverter" />
                  </h:outputText>
                  <h:outputText value="Nome: " styleClass="Fs12 FontBold" />
                  <h:outputText value="#{acumulacao.nome}" styleClass="Fs12" />
                  <h:outputText value="Situação: " styleClass="Fs12 FontBold" />
                  <h:outputText
                    value="#{acumulacao.situacaoTexto}"
                    styleClass="Fs12"
                  />
                </h:panelGrid>
              </p:tab>
              <p:tab title="Vinculos">
                <p:dataTable
                  id="tblDetail"
                  var="detalhamentoAcumulacao"
                  widgetVar="tblDetailVar"
                  tableStyle="table-layout: auto"
                  reflow="true"
                  value="#{detalhamentoAcumulacaoBean.acumulacao.listaDetalhamentoAcumulacao}"
                  emptyMessage="Nenhum acumulação encontrada."
                  rows="20"
                  paginator="true"
                  paginatorAlwaysVisible="false"
                  paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                  rowsPerPageTemplate="20,30,40,50,100,300,500, 1000"
                  currentPageReportTemplate="página {currentPage} de {totalPages}"
                  paginatorPosition="bottom"
                >
                  <p:column headerText="Ação" styleClass="TexAlCenter">
                    <p:commandButton
                      title="Contra Cheque"
                      style="width: auto"
                      action="#{auditoriaContraChequeBean.carregaContraCheque()}"
                      actionListener="#{auditoriaContraChequeBean.carregaParametros}"
                      icon="fa fa-fw fa-money white"
                      styleClass="autoWidth"
                      update="@form :frmContraCheque"
                      oncomplete="PF('dlgCCVar').show()"
                    >
                      <f:attribute
                        name="beneficiario"
                        value="#{detalhamentoAcumulacao.beneficiario.id}"
                      />
                      <f:attribute
                        name="ano"
                        value="#{detalhamentoAcumulacao.ano}"
                      />
                      <f:attribute
                        name="mes"
                        value="#{detalhamentoAcumulacao.mes.numero}"
                      />
                    </p:commandButton>
                  </p:column>
                  <p:column
                    headerText="Entidade"
                    width="25%"
                    styleClass="TexAlCenter"
                  >
                    <h:outputText
                      value="#{detalhamentoAcumulacao.entidade.nome}"
                    />
                  </p:column>
                  <p:column headerText="Município" styleClass="TexAlCenter">
                    <h:outputText
                      value="#{detalhamentoAcumulacao.municipio.nome}"
                    />
                  </p:column>
                  <p:column headerText="Matrícula" styleClass="TexAlCenter">
                    <h:outputText
                      value="#{detalhamentoAcumulacao.beneficiario.matricula}"
                    />
                  </p:column>
                  <p:column headerText="Cargo" styleClass="TexAlCenter">
                    <h:outputText value="#{detalhamentoAcumulacao.cargoNome}" />
                  </p:column>
                  <p:column
                    headerText="Cargo Referência"
                    styleClass="TexAlCenter"
                  >
                    <h:outputText
                      value="#{detalhamentoAcumulacao.cargo.referenciaSub.descricao}"
                    />
                  </p:column>
                  <p:column headerText="Tipo de Cargo" styleClass="TexAlCenter">
                    <h:outputText
                      value="#{detalhamentoAcumulacao.cargo.tipo.descricao}"
                    />
                  </p:column>

                  <p:column headerText="Valor Líquido" styleClass="TexAlCenter">
                    <h:outputText
                      value="#{detalhamentoAcumulacao.totalLiquido}"
                      style="float: center"
                    >
                      <f:convertNumber pattern="R$ ###,##0.00" locale="pt_br" />
                    </h:outputText>
                  </p:column>

                  <p:column
                    headerText="Carga Horária Semanal"
                    styleClass="TexAlCenter"
                  >
                    <!--<h:outputText
                      value="#{detalhamentoAcumulacao.cargo.cargaHorariaMensal}"
                    />-->
                    <h:outputText
                      value="#{detalhamentoAcumulacao.cargaHorariaSemanal}"
                    />
                  </p:column>

                  <f:facet name="footer">
                    <div
                      style="
                        display: flex;
                        justify-content: space-between;
                        width: 100%;
                      "
                    >
                      <h:outputLabel
                        value="Total de acumulações: #{detalhamentoAcumulacaoBean.acumulacao.listaDetalhamentoAcumulacao.size()}"
                      />
                      <h:outputLabel style="color: grey; font-weight: bold">
                        <h:outputText value="CH Semanal Total: " />
                        <h:outputText
                          value="#{detalhamentoAcumulacaoBean.cargaHorariaSemanalTotal}"
                        />
                      </h:outputLabel>
                    </div>
                  </f:facet>
                </p:dataTable>
              </p:tab>
              <p:tab title="Relatório de Acumulações">
                <h:panelGrid columns="2">
                  <p:commandButton
                    value="Relatório de Acumulações"
                    icon="fa fa-fw fa-file-pdf-o white"
                    actionListener="#{acumulacaoBean.gerarRelatorioAcumulacao()}"
                    ajax="false"
                  />
                  <p:commandButton
                    value="Relatório Financeiro de Acumulações e Verbas"
                    icon="fa fa-fw fa-file-pdf-o white"
                    actionListener="#{acumulacaoBean.gerarRelatorioAcumulacaoVerbas()}"
                    ajax="false"
                  />
                </h:panelGrid>
              </p:tab>
            </p:accordionPanel>
            <div class="EmptyBox10" />
            <h:panelGrid columns="2">
              <p:commandButton
                value="Voltar"
                style="margin-top: 10px"
                icon="ui-icon-arrowthick-1-w"
                process="@this"
                immediate="true"
              >
                <pe:selectDetailLevel step="-1" />
              </p:commandButton>
            </h:panelGrid>
          </pe:masterDetailLevel>
        </pe:masterDetail>
      </p:fieldset>
    </h:form>
    <p:dialog
      id="dlgCC"
      widgetVar="dlgCCVar"
      modal="true"
      header="Contracheque"
      showEffect="fade"
      hideEffect="fade"
      resizable="false"
      closeOnEscape="true"
      position="top"
      style="margin: 25px 0px 25px"
      responsive="true"
      width="80%"
      fitViewport="true"
    >
      <ui:include src="contracheque.xhtml" />
    </p:dialog>
    <p:dialog
      id="dlgIniciarDistribuicao"
      closeOnEscape="true"
      responsive="true"
      widgetVar="dlgIniciarDistribuicaoVar"
      showEffect="fade"
      modal="true"
      header="Distribuição de Acumulações"
      width="80%"
      position="top"
      style="margin: 25px 0px 25px"
      fitViewport="true"
      visible="#{not empty acumulacaoBean.acumulacoesSelecionadas}"
      hideEffect="fade"
      resizable="false"
    >
      <ui:include src="/auditoria/distribuicao/distribuir.xhtml" />
    </p:dialog>
  </ui:define>
</ui:composition>
